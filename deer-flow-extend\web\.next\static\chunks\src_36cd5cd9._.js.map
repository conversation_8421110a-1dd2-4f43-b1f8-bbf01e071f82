{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/lib/utils.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(\r\n        buttonVariants({ variant, size, className }),\r\n        \"cursor-pointer active:scale-105\",\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,eAAe;YAAE;YAAS;YAAM;QAAU,IAC1C;QAED,GAAG,KAAK;;;;;;AAGf;KAtBS", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/logo.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport Link from \"next/link\";\r\n\r\nexport function Logo() {\r\n  return (\r\n    <Link\r\n      className=\"opacity-70 transition-opacity duration-300 hover:opacity-100\"\r\n      href=\"/\"\r\n    >\r\n      🦌 DeerFlow\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;;;AAEO,SAAS;IACd,qBACE,sSAAC,wQAAA,CAAA,UAAI;QACH,WAAU;QACV,MAAK;kBACN;;;;;;AAIL;KATgB", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,sSAAC,sRAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,sSAAC,sRAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,sSAAC,sRAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,sSAAC,sRAAA,CAAA,SAA4B;kBAC3B,cAAA,sSAAC,sRAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,sSAAC,sRAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,sSAAC,sRAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,sSAAC,sRAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,sSAAC;gBAAK,WAAU;0BACd,cAAA,sSAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,sSAAC,+RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,sSAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,sSAAC,sRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,sSAAC;gBAAK,WAAU;0BACd,cAAA,sSAAC,sRAAA,CAAA,gBAAmC;8BAClC,cAAA,sSAAC,iSAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,sSAAC,sRAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,sSAAC,sRAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,sSAAC,sRAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,sSAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,sSAAC,iTAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,sSAAC,sRAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/tooltip.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />;\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className,\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  );\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;AAK/B;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,sSAAC,gRAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,sSAAC;kBACC,cAAA,sSAAC,gRAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,sSAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,sSAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,sSAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,sSAAC,gRAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/tooltip.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport type { CSSProperties } from \"react\";\r\n\r\nimport {\r\n  Tooltip as ShadcnTooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"~/components/ui/tooltip\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function Tooltip({\r\n  className,\r\n  style,\r\n  children,\r\n  title,\r\n  open,\r\n  side,\r\n  sideOffset,\r\n  delayDuration = 750,\r\n}: {\r\n  className?: string;\r\n  style?: CSSProperties;\r\n  children: React.ReactNode;\r\n  title?: React.ReactNode;\r\n  open?: boolean;\r\n  side?: \"left\" | \"right\" | \"top\" | \"bottom\";\r\n  sideOffset?: number;\r\n  delayDuration?: number;\r\n}) {\r\n  return (\r\n    <TooltipProvider>\r\n      <ShadcnTooltip delayDuration={delayDuration} open={open}>\r\n        <TooltipTrigger asChild>{children}</TooltipTrigger>\r\n        <TooltipContent\r\n          className={cn(className)}\r\n          style={style}\r\n          side={side}\r\n          sideOffset={sideOffset}\r\n        >\r\n          {title}\r\n        </TooltipContent>\r\n      </ShadcnTooltip>\r\n    </TooltipProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAMA;;;;AAEO,SAAS,QAAQ,EACtB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,gBAAgB,GAAG,EAUpB;IACC,qBACE,sSAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,sSAAC,sIAAA,CAAA,UAAa;YAAC,eAAe;YAAe,MAAM;;8BACjD,sSAAC,sIAAA,CAAA,iBAAc;oBAAC,OAAO;8BAAE;;;;;;8BACzB,sSAAC,sIAAA,CAAA,iBAAc;oBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;8BAEX;;;;;;;;;;;;;;;;;AAKX;KAlCgB", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/theme-toggle.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { Monitor, Moon, Sun } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\n\r\nimport { But<PERSON> } from \"~/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"~/components/ui/dropdown-menu\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { Tooltip } from \"./tooltip\";\r\n\r\nexport function ThemeToggle() {\r\n  const { theme = \"system\", setTheme } = useTheme();\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <Tooltip title=\"Change theme\">\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" size=\"icon\">\r\n            <Sun className=\"h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90\" />\r\n            <Moon className=\"absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n      </Tooltip>\r\n      <DropdownMenuContent align=\"end\">\r\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n          <Sun className=\"mr-2 h-4 w-4\" />\r\n          <span\r\n            className={cn(\r\n              theme === \"light\" ? \"font-bold\" : \"text-muted-foreground\",\r\n            )}\r\n          >\r\n            Light\r\n          </span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n          <Moon className=\"mr-2 h-4 w-4\" />\r\n          <span\r\n            className={cn(\r\n              theme === \"dark\" ? \"font-bold\" : \"text-muted-foreground\",\r\n            )}\r\n          >\r\n            Dark\r\n          </span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n          <Monitor className=\"mr-2 h-4 w-4\" />\r\n          <span\r\n            className={cn(\r\n              theme === \"system\" ? \"font-bold\" : \"text-muted-foreground\",\r\n            )}\r\n          >\r\n            System\r\n          </span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAAA;AAAA;AACA;AAEA;AACA;AAMA;AAEA;;;AAdA;;;;;;;AAgBO,SAAS;;IACd,MAAM,EAAE,QAAQ,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IAE9C,qBACE,sSAAC,+IAAA,CAAA,eAAY;;0BACX,sSAAC,gJAAA,CAAA,UAAO;gBAAC,OAAM;0BACb,cAAA,sSAAC,+IAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,sSAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;;0CAC3B,sSAAC,uRAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,sSAAC,yRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAItB,sSAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,sSAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,sSAAC,uRAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,sSAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,UAAU,UAAU,cAAc;0CAErC;;;;;;;;;;;;kCAIH,sSAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,sSAAC,yRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,sSAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,UAAU,SAAS,cAAc;0CAEpC;;;;;;;;;;;;kCAIH,sSAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,sSAAC,+RAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,sSAAC;gCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,UAAU,WAAW,cAAc;0CAEtC;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA/CgB;;QACyB,4PAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,sSAAC,kRAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,sSAAC,kRAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,sSAAC,kRAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,sSAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,sSAAC,kRAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,sSAAC;QAAa,aAAU;;0BACtB,sSAAC;;;;;0BACD,sSAAC,kRAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,sSAAC,kRAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,sSAAC,uRAAA,CAAA,QAAK;;;;;0CACN,sSAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,sSAAC,kRAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/tabs.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;AAK/B;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,sSAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/env.js"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { createEnv } from \"@t3-oss/env-nextjs\";\r\nimport { z } from \"zod\";\r\n\r\nexport const env = createEnv({\r\n  /**\r\n   * Specify your server-side environment variables schema here. This way you can ensure the app\r\n   * isn't built with invalid env vars.\r\n   */\r\n  server: {\r\n    NODE_ENV: z.enum([\"development\", \"test\", \"production\"]),\r\n    AMPLITUDE_API_KEY: z.string().optional(),\r\n    GITHUB_OAUTH_TOKEN: z.string().optional(),\r\n  },\r\n\r\n  /**\r\n   * Specify your client-side environment variables schema here. This way you can ensure the app\r\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n   * `NEXT_PUBLIC_`.\r\n   */\r\n  client: {\r\n    NEXT_PUBLIC_API_URL: z.string().optional(),\r\n    NEXT_PUBLIC_STATIC_WEBSITE_ONLY: z.boolean().optional(),\r\n  },\r\n\r\n  /**\r\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n   * middlewares) or client-side so we need to destruct manually.\r\n   */\r\n  runtimeEnv: {\r\n    NODE_ENV: process.env.NODE_ENV,\r\n    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n    NEXT_PUBLIC_STATIC_WEBSITE_ONLY:\r\n      process.env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY === \"true\",\r\n    AMPLITUDE_API_KEY: process.env.AMPLITUDE_API_KEY,\r\n    GITHUB_OAUTH_TOKEN: process.env.GITHUB_OAUTH_TOKEN,\r\n  },\r\n  /**\r\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n   * useful for Docker builds.\r\n   */\r\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n  /**\r\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n   * `SOME_VAR=''` will throw an error.\r\n   */\r\n  emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AA+BjB;AA7Bd;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,wRAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,UAAU,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,oBAAoB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzC;IAEA;;;;GAIC,GACD,QAAQ;QACN,qBAAqB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxC,iCAAiC,wLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IACvD;IAEA;;;GAGC,GACD,YAAY;QACV,QAAQ;QACR,qBAAqB,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;QACpD,iCACE,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,+BAA+B,KAAK;QAClD,mBAAmB,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;QAChD,oBAAoB,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB;IACpD;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,yQAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 1135, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/replay/get-replay-id.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport function extractReplayIdFromSearchParams(params: string) {\r\n  const urlParams = new URLSearchParams(params);\r\n  if (urlParams.has(\"replay\")) {\r\n    return urlParams.get(\"replay\");\r\n  }\r\n  return null;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAExB,SAAS,gCAAgC,MAAc;IAC5D,MAAM,YAAY,IAAI,gBAAgB;IACtC,IAAI,UAAU,GAAG,CAAC,WAAW;QAC3B,OAAO,UAAU,GAAG,CAAC;IACvB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/replay/hooks.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { useMemo } from \"react\";\r\n\r\nimport { env } from \"~/env\";\r\n\r\nimport { extractReplayIdFromSearchParams } from \"./get-replay-id\";\r\n\r\nexport function useReplay() {\r\n  const searchParams = useSearchParams();\r\n  const replayId = useMemo(\r\n    () => extractReplayIdFromSearchParams(searchParams.toString()),\r\n    [searchParams],\r\n  );\r\n  return {\r\n    isReplay: replayId != null || env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY,\r\n    replayId,\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AACA;AAEA;AAEA;;;;;;AAEO,SAAS;;IACd,MAAM,eAAe,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;uCACrB,IAAM,CAAA,GAAA,+IAAA,CAAA,kCAA+B,AAAD,EAAE,aAAa,QAAQ;sCAC3D;QAAC;KAAa;IAEhB,OAAO;QACL,UAAU,YAAY,QAAQ,6GAAA,CAAA,MAAG,CAAC,+BAA+B;QACjE;IACF;AACF;GAVgB;;QACO,8OAAA,CAAA,kBAAe", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/replay/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./hooks\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B", "debugId": null}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/sse/fetch-stream.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { type StreamEvent } from \"./StreamEvent\";\r\n\r\nexport async function* fetchStream(\r\n  url: string,\r\n  init: RequestInit,\r\n): AsyncIterable<StreamEvent> {\r\n  const response = await fetch(url, {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      \"Cache-Control\": \"no-cache\",\r\n    },\r\n    ...init,\r\n  });\r\n  if (response.status !== 200) {\r\n    throw new Error(`Failed to fetch from ${url}: ${response.status}`);\r\n  }\r\n  // Read from response body, event by event. An event always ends with a '\\n\\n'.\r\n  const reader = response.body\r\n    ?.pipeThrough(new TextDecoderStream())\r\n    .getReader();\r\n  if (!reader) {\r\n    throw new Error(\"Response body is not readable\");\r\n  }\r\n  let buffer = \"\";\r\n  while (true) {\r\n    const { done, value } = await reader.read();\r\n    if (done) {\r\n      break;\r\n    }\r\n    buffer += value;\r\n    while (true) {\r\n      const index = buffer.indexOf(\"\\n\\n\");\r\n      if (index === -1) {\r\n        break;\r\n      }\r\n      const chunk = buffer.slice(0, index);\r\n      buffer = buffer.slice(index + 2);\r\n      const event = parseEvent(chunk);\r\n      if (event) {\r\n        yield event;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nfunction parseEvent(chunk: string) {\r\n  let resultEvent = \"message\";\r\n  let resultData: string | null = null;\r\n  for (const line of chunk.split(\"\\n\")) {\r\n    const pos = line.indexOf(\": \");\r\n    if (pos === -1) {\r\n      continue;\r\n    }\r\n    const key = line.slice(0, pos);\r\n    const value = line.slice(pos + 2);\r\n    if (key === \"event\") {\r\n      resultEvent = value;\r\n    } else if (key === \"data\") {\r\n      resultData = value;\r\n    }\r\n  }\r\n  if (resultEvent === \"message\" && resultData === null) {\r\n    return undefined;\r\n  }\r\n  return {\r\n    event: resultEvent,\r\n    data: resultData,\r\n  } as StreamEvent;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAIxB,gBAAgB,YACrB,GAAW,EACX,IAAiB;IAEjB,MAAM,WAAW,MAAM,MAAM,KAAK;QAChC,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;QACA,GAAG,IAAI;IACT;IACA,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,IAAI,EAAE,EAAE,SAAS,MAAM,EAAE;IACnE;IACA,+EAA+E;IAC/E,MAAM,SAAS,SAAS,IAAI,EACxB,YAAY,IAAI,qBACjB;IACH,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,SAAS;IACb,MAAO,KAAM;QACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;QACzC,IAAI,MAAM;YACR;QACF;QACA,UAAU;QACV,MAAO,KAAM;YACX,MAAM,QAAQ,OAAO,OAAO,CAAC;YAC7B,IAAI,UAAU,CAAC,GAAG;gBAChB;YACF;YACA,MAAM,QAAQ,OAAO,KAAK,CAAC,GAAG;YAC9B,SAAS,OAAO,KAAK,CAAC,QAAQ;YAC9B,MAAM,QAAQ,WAAW;YACzB,IAAI,OAAO;gBACT,MAAM;YACR;QACF;IACF;AACF;AAEA,SAAS,WAAW,KAAa;IAC/B,IAAI,cAAc;IAClB,IAAI,aAA4B;IAChC,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAC,MAAO;QACpC,MAAM,MAAM,KAAK,OAAO,CAAC;QACzB,IAAI,QAAQ,CAAC,GAAG;YACd;QACF;QACA,MAAM,MAAM,KAAK,KAAK,CAAC,GAAG;QAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM;QAC/B,IAAI,QAAQ,SAAS;YACnB,cAAc;QAChB,OAAO,IAAI,QAAQ,QAAQ;YACzB,aAAa;QACf;IACF;IACA,IAAI,gBAAgB,aAAa,eAAe,MAAM;QACpD,OAAO;IACT;IACA,OAAO;QACL,OAAO;QACP,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1296, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/sse/StreamEvent.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport interface StreamEvent {\r\n  event: string;\r\n  data: string;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/sse/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./fetch-stream\";\r\nexport * from \"./StreamEvent\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B;AACA", "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/utils/time.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport function sleep(ms: number) {\r\n  return new Promise((resolve) => setTimeout(resolve, ms));\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAExB,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;AACtD", "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/utils/json.ts"], "sourcesContent": ["import { parse } from \"best-effort-json-parser\";\r\n\r\nexport function parseJSON<T>(json: string | null | undefined, fallback: T) {\r\n  if (!json) {\r\n    return fallback;\r\n  }\r\n  try {\r\n    const raw = json\r\n      .trim()\r\n      .replace(/^```json\\s*/, \"\")\r\n      .replace(/^```\\s*/, \"\")\r\n      .replace(/\\s*```$/, \"\");\r\n    return parse(raw) as T;\r\n  } catch {\r\n    return fallback;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,UAAa,IAA+B,EAAE,QAAW;IACvE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,IAAI;QACF,MAAM,MAAM,KACT,IAAI,GACJ,OAAO,CAAC,eAAe,IACvB,OAAO,CAAC,WAAW,IACnB,OAAO,CAAC,WAAW;QACtB,OAAO,CAAA,GAAA,iPAAA,CAAA,QAAK,AAAD,EAAE;IACf,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/utils/deep-clone.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport function deepClone<T>(value: T): T {\r\n  return JSON.parse(JSON.stringify(value));\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAExB,SAAS,UAAa,KAAQ;IACnC,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;AACnC", "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/utils/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./time\";\r\nexport * from \"./json\";\r\nexport * from \"./deep-clone\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B;AACA;AACA", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/api/resolve-service-url.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { env } from \"~/env\";\r\n\r\nexport function resolveServiceURL(path: string) {\r\n  let BASE_URL = env.NEXT_PUBLIC_API_URL ?? \"http://localhost:8000/api/\";\r\n  if (!BASE_URL.endsWith(\"/\")) {\r\n    BASE_URL += \"/\";\r\n  }\r\n  return new URL(path, BASE_URL).toString();\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;;AAEO,SAAS,kBAAkB,IAAY;IAC5C,IAAI,WAAW,6GAAA,CAAA,MAAG,CAAC,mBAAmB,IAAI;IAC1C,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM;QAC3B,YAAY;IACd;IACA,OAAO,IAAI,IAAI,MAAM,UAAU,QAAQ;AACzC", "debugId": null}}, {"offset": {"line": 1446, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/api/chat.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { env } from \"~/env\";\r\n\r\nimport type { MCPServerMetadata } from \"../mcp\";\r\nimport { extractReplayIdFromSearchParams } from \"../replay/get-replay-id\";\r\nimport { fetchStream } from \"../sse\";\r\nimport { sleep } from \"../utils\";\r\n\r\nimport { resolveServiceURL } from \"./resolve-service-url\";\r\nimport type { ChatEvent } from \"./types\";\r\n\r\nexport async function* chatStream(\r\n  userMessage: string,\r\n  params: {\r\n    thread_id: string;\r\n    auto_accepted_plan: boolean;\r\n    max_plan_iterations: number;\r\n    max_step_num: number;\r\n    max_search_results?: number;\r\n    interrupt_feedback?: string;\r\n    enable_background_investigation: boolean;\r\n    mcp_settings?: {\r\n      servers: Record<\r\n        string,\r\n        MCPServerMetadata & {\r\n          enabled_tools: string[];\r\n          add_to_agents: string[];\r\n        }\r\n      >;\r\n    };\r\n  },\r\n  options: { abortSignal?: AbortSignal } = {},\r\n) {\r\n  if (\r\n    env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY ||\r\n    location.search.includes(\"mock\") ||\r\n    location.search.includes(\"replay=\")\r\n  ) {\r\n    return yield* chatReplayStream(userMessage, params, options);\r\n  }\r\n  const stream = fetchStream(resolveServiceURL(\"chat/stream\"), {\r\n    body: JSON.stringify({\r\n      messages: [{ role: \"user\", content: userMessage }],\r\n      ...params,\r\n    }),\r\n    signal: options.abortSignal,\r\n  });\r\n  for await (const event of stream) {\r\n    yield {\r\n      type: event.event,\r\n      data: JSON.parse(event.data),\r\n    } as ChatEvent;\r\n  }\r\n}\r\n\r\nasync function* chatReplayStream(\r\n  userMessage: string,\r\n  params: {\r\n    thread_id: string;\r\n    auto_accepted_plan: boolean;\r\n    max_plan_iterations: number;\r\n    max_step_num: number;\r\n    max_search_results?: number;\r\n    interrupt_feedback?: string;\r\n  } = {\r\n    thread_id: \"__mock__\",\r\n    auto_accepted_plan: false,\r\n    max_plan_iterations: 3,\r\n    max_step_num: 1,\r\n    max_search_results: 3,\r\n    interrupt_feedback: undefined,\r\n  },\r\n  options: { abortSignal?: AbortSignal } = {},\r\n): AsyncIterable<ChatEvent> {\r\n  const urlParams = new URLSearchParams(window.location.search);\r\n  let replayFilePath = \"\";\r\n  if (urlParams.has(\"mock\")) {\r\n    if (urlParams.get(\"mock\")) {\r\n      replayFilePath = `/mock/${urlParams.get(\"mock\")!}.txt`;\r\n    } else {\r\n      if (params.interrupt_feedback === \"accepted\") {\r\n        replayFilePath = \"/mock/final-answer.txt\";\r\n      } else if (params.interrupt_feedback === \"edit_plan\") {\r\n        replayFilePath = \"/mock/re-plan.txt\";\r\n      } else {\r\n        replayFilePath = \"/mock/first-plan.txt\";\r\n      }\r\n    }\r\n    fastForwardReplaying = true;\r\n  } else {\r\n    const replayId = extractReplayIdFromSearchParams(window.location.search);\r\n    if (replayId) {\r\n      replayFilePath = `/replay/${replayId}.txt`;\r\n    } else {\r\n      // Fallback to a default replay\r\n      replayFilePath = `/replay/eiffel-tower-vs-tallest-building.txt`;\r\n    }\r\n  }\r\n  const text = await fetchReplay(replayFilePath, {\r\n    abortSignal: options.abortSignal,\r\n  });\r\n  const chunks = text.split(\"\\n\\n\");\r\n  for (const chunk of chunks) {\r\n    const [eventRaw, dataRaw] = chunk.split(\"\\n\") as [string, string];\r\n    const [, event] = eventRaw.split(\"event: \", 2) as [string, string];\r\n    const [, data] = dataRaw.split(\"data: \", 2) as [string, string];\r\n\r\n    try {\r\n      const chatEvent = {\r\n        type: event,\r\n        data: JSON.parse(data),\r\n      } as ChatEvent;\r\n      if (chatEvent.type === \"message_chunk\") {\r\n        if (!chatEvent.data.finish_reason) {\r\n          await sleepInReplay(50);\r\n        }\r\n      } else if (chatEvent.type === \"tool_call_result\") {\r\n        await sleepInReplay(500);\r\n      }\r\n      yield chatEvent;\r\n      if (chatEvent.type === \"tool_call_result\") {\r\n        await sleepInReplay(800);\r\n      } else if (chatEvent.type === \"message_chunk\") {\r\n        if (chatEvent.data.role === \"user\") {\r\n          await sleepInReplay(500);\r\n        }\r\n      }\r\n    } catch (e) {\r\n      console.error(e);\r\n    }\r\n  }\r\n}\r\n\r\nconst replayCache = new Map<string, string>();\r\nexport async function fetchReplay(\r\n  url: string,\r\n  options: { abortSignal?: AbortSignal } = {},\r\n) {\r\n  if (replayCache.has(url)) {\r\n    return replayCache.get(url)!;\r\n  }\r\n  const res = await fetch(url, {\r\n    signal: options.abortSignal,\r\n  });\r\n  if (!res.ok) {\r\n    throw new Error(`Failed to fetch replay: ${res.statusText}`);\r\n  }\r\n  const text = await res.text();\r\n  replayCache.set(url, text);\r\n  return text;\r\n}\r\n\r\nexport async function fetchReplayTitle() {\r\n  const res = chatReplayStream(\r\n    \"\",\r\n    {\r\n      thread_id: \"__mock__\",\r\n      auto_accepted_plan: false,\r\n      max_plan_iterations: 3,\r\n      max_step_num: 1,\r\n      max_search_results: 3,\r\n    },\r\n    {},\r\n  );\r\n  for await (const event of res) {\r\n    if (event.type === \"message_chunk\") {\r\n      return event.data.content;\r\n    }\r\n  }\r\n}\r\n\r\nexport async function sleepInReplay(ms: number) {\r\n  if (fastForwardReplaying) {\r\n    await sleep(0);\r\n  } else {\r\n    await sleep(ms);\r\n  }\r\n}\r\n\r\nlet fastForwardReplaying = false;\r\nexport function fastForwardReplay(value: boolean) {\r\n  fastForwardReplaying = value;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;AAE/B;AAGA;AACA;AAAA;AACA;AAAA;AAEA;;;;;;AAGO,gBAAgB,WACrB,WAAmB,EACnB,MAiBC,EACD,UAAyC,CAAC,CAAC;IAE3C,IACE,6GAAA,CAAA,MAAG,CAAC,+BAA+B,IACnC,SAAS,MAAM,CAAC,QAAQ,CAAC,WACzB,SAAS,MAAM,CAAC,QAAQ,CAAC,YACzB;QACA,OAAO,OAAO,iBAAiB,aAAa,QAAQ;IACtD;IACA,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;QAC3D,MAAM,KAAK,SAAS,CAAC;YACnB,UAAU;gBAAC;oBAAE,MAAM;oBAAQ,SAAS;gBAAY;aAAE;YAClD,GAAG,MAAM;QACX;QACA,QAAQ,QAAQ,WAAW;IAC7B;IACA,WAAW,MAAM,SAAS,OAAQ;QAChC,MAAM;YACJ,MAAM,MAAM,KAAK;YACjB,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI;QAC7B;IACF;AACF;AAEA,gBAAgB,iBACd,WAAmB,EACnB,SAOI;IACF,WAAW;IACX,oBAAoB;IACpB,qBAAqB;IACrB,cAAc;IACd,oBAAoB;IACpB,oBAAoB;AACtB,CAAC,EACD,UAAyC,CAAC,CAAC;IAE3C,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;IAC5D,IAAI,iBAAiB;IACrB,IAAI,UAAU,GAAG,CAAC,SAAS;QACzB,IAAI,UAAU,GAAG,CAAC,SAAS;YACzB,iBAAiB,CAAC,MAAM,EAAE,UAAU,GAAG,CAAC,QAAS,IAAI,CAAC;QACxD,OAAO;YACL,IAAI,OAAO,kBAAkB,KAAK,YAAY;gBAC5C,iBAAiB;YACnB,OAAO,IAAI,OAAO,kBAAkB,KAAK,aAAa;gBACpD,iBAAiB;YACnB,OAAO;gBACL,iBAAiB;YACnB;QACF;QACA,uBAAuB;IACzB,OAAO;QACL,MAAM,WAAW,CAAA,GAAA,+IAAA,CAAA,kCAA+B,AAAD,EAAE,OAAO,QAAQ,CAAC,MAAM;QACvE,IAAI,UAAU;YACZ,iBAAiB,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC;QAC5C,OAAO;YACL,+BAA+B;YAC/B,iBAAiB,CAAC,4CAA4C,CAAC;QACjE;IACF;IACA,MAAM,OAAO,MAAM,YAAY,gBAAgB;QAC7C,aAAa,QAAQ,WAAW;IAClC;IACA,MAAM,SAAS,KAAK,KAAK,CAAC;IAC1B,KAAK,MAAM,SAAS,OAAQ;QAC1B,MAAM,CAAC,UAAU,QAAQ,GAAG,MAAM,KAAK,CAAC;QACxC,MAAM,GAAG,MAAM,GAAG,SAAS,KAAK,CAAC,WAAW;QAC5C,MAAM,GAAG,KAAK,GAAG,QAAQ,KAAK,CAAC,UAAU;QAEzC,IAAI;YACF,MAAM,YAAY;gBAChB,MAAM;gBACN,MAAM,KAAK,KAAK,CAAC;YACnB;YACA,IAAI,UAAU,IAAI,KAAK,iBAAiB;gBACtC,IAAI,CAAC,UAAU,IAAI,CAAC,aAAa,EAAE;oBACjC,MAAM,cAAc;gBACtB;YACF,OAAO,IAAI,UAAU,IAAI,KAAK,oBAAoB;gBAChD,MAAM,cAAc;YACtB;YACA,MAAM;YACN,IAAI,UAAU,IAAI,KAAK,oBAAoB;gBACzC,MAAM,cAAc;YACtB,OAAO,IAAI,UAAU,IAAI,KAAK,iBAAiB;gBAC7C,IAAI,UAAU,IAAI,CAAC,IAAI,KAAK,QAAQ;oBAClC,MAAM,cAAc;gBACtB;YACF;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC;QAChB;IACF;AACF;AAEA,MAAM,cAAc,IAAI;AACjB,eAAe,YACpB,GAAW,EACX,UAAyC,CAAC,CAAC;IAE3C,IAAI,YAAY,GAAG,CAAC,MAAM;QACxB,OAAO,YAAY,GAAG,CAAC;IACzB;IACA,MAAM,MAAM,MAAM,MAAM,KAAK;QAC3B,QAAQ,QAAQ,WAAW;IAC7B;IACA,IAAI,CAAC,IAAI,EAAE,EAAE;QACX,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,IAAI,UAAU,EAAE;IAC7D;IACA,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,YAAY,GAAG,CAAC,KAAK;IACrB,OAAO;AACT;AAEO,eAAe;IACpB,MAAM,MAAM,iBACV,IACA;QACE,WAAW;QACX,oBAAoB;QACpB,qBAAqB;QACrB,cAAc;QACd,oBAAoB;IACtB,GACA,CAAC;IAEH,WAAW,MAAM,SAAS,IAAK;QAC7B,IAAI,MAAM,IAAI,KAAK,iBAAiB;YAClC,OAAO,MAAM,IAAI,CAAC,OAAO;QAC3B;IACF;AACF;AAEO,eAAe,cAAc,EAAU;IAC5C,IAAI,sBAAsB;QACxB,MAAM,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;IACd,OAAO;QACL,MAAM,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;IACd;AACF;AAEA,IAAI,uBAAuB;AACpB,SAAS,kBAAkB,KAAc;IAC9C,uBAAuB;AACzB", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/api/mcp.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport type { SimpleMCPServerMetadata } from \"../mcp\";\r\n\r\nimport { resolveServiceURL } from \"./resolve-service-url\";\r\n\r\nexport async function queryMCPServerMetadata(config: SimpleMCPServerMetadata) {\r\n  const response = await fetch(resolveServiceURL(\"mcp/server/metadata\"), {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify(config),\r\n  });\r\n  if (!response.ok) {\r\n    throw new Error(`HTTP error! status: ${response.status}`);\r\n  }\r\n  return response.json();\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAI/B;;AAEO,eAAe,uBAAuB,MAA+B;IAC1E,MAAM,WAAW,MAAM,MAAM,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,wBAAwB;QACrE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC1D;IACA,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1633, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/api/podcast.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { resolveServiceURL } from \"./resolve-service-url\";\r\n\r\nexport async function generatePodcast(content: string) {\r\n  const response = await fetch(resolveServiceURL(\"podcast/generate\"), {\r\n    method: \"post\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({ content }),\r\n  });\r\n  if (!response.ok) {\r\n    throw new Error(`HTTP error! status: ${response.status}`);\r\n  }\r\n  const arrayBuffer = await response.arrayBuffer();\r\n  const blob = new Blob([arrayBuffer], { type: \"audio/mp3\" });\r\n  const audioUrl = URL.createObjectURL(blob);\r\n  return audioUrl;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;;AAEO,eAAe,gBAAgB,OAAe;IACnD,MAAM,WAAW,MAAM,MAAM,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,qBAAqB;QAClE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;QAAQ;IACjC;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC1D;IACA,MAAM,cAAc,MAAM,SAAS,WAAW;IAC9C,MAAM,OAAO,IAAI,KAAK;QAAC;KAAY,EAAE;QAAE,MAAM;IAAY;IACzD,MAAM,WAAW,IAAI,eAAe,CAAC;IACrC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/api/types.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport type { Option } from \"../messages\";\r\n\r\n// Tool Calls\r\n\r\nexport interface ToolCall {\r\n  type: \"tool_call\";\r\n  id: string;\r\n  name: string;\r\n  args: Record<string, unknown>;\r\n}\r\n\r\nexport interface ToolCallChunk {\r\n  type: \"tool_call_chunk\";\r\n  index: number;\r\n  id: string;\r\n  name: string;\r\n  args: string;\r\n}\r\n\r\n// Events\r\n\r\ninterface GenericEvent<T extends string, D extends object> {\r\n  type: T;\r\n  data: {\r\n    id: string;\r\n    thread_id: string;\r\n    agent: \"coordinator\" | \"planner\" | \"researcher\" | \"coder\" | \"reporter\" | \r\n           \"kb_builder\" | \"kb_planner\" | \"kb_researcher\" | \"kb_processor\" | \"kb_injector\";\r\n    role: \"user\" | \"assistant\" | \"tool\";\r\n    finish_reason?: \"stop\" | \"tool_calls\" | \"interrupt\";\r\n  } & D;\r\n}\r\n\r\nexport interface MessageChunkEvent\r\n  extends GenericEvent<\r\n    \"message_chunk\",\r\n    {\r\n      content?: string;\r\n    }\r\n  > {}\r\n\r\nexport interface ToolCallsEvent\r\n  extends GenericEvent<\r\n    \"tool_calls\",\r\n    {\r\n      tool_calls: ToolCall[];\r\n      tool_call_chunks: ToolCallChunk[];\r\n    }\r\n  > {}\r\n\r\nexport interface ToolCallChunksEvent\r\n  extends GenericEvent<\r\n    \"tool_call_chunks\",\r\n    {\r\n      tool_call_chunks: ToolCallChunk[];\r\n    }\r\n  > {}\r\n\r\nexport interface ToolCallResultEvent\r\n  extends GenericEvent<\r\n    \"tool_call_result\",\r\n    {\r\n      tool_call_id: string;\r\n      content?: string;\r\n    }\r\n  > {}\r\n\r\nexport interface InterruptEvent\r\n  extends GenericEvent<\r\n    \"interrupt\",\r\n    {\r\n      options: Option[];\r\n    }\r\n  > {}\r\n\r\nexport type ChatEvent =\r\n  | MessageChunkEvent\r\n  | ToolCallsEvent\r\n  | ToolCallChunksEvent\r\n  | ToolCallResultEvent\r\n  | InterruptEvent;"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B", "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/api/kb.ts"], "sourcesContent": ["// web/src/core/api/kb.ts\r\n// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { fetchStream } from \"../sse\";\r\nimport { resolveServiceURL } from \"./resolve-service-url\";\r\n\r\nexport interface BuildKnowledgeBaseRequest {\r\n  topics: string[];\r\n  target_article_count: number;\r\n  industries?: string[];\r\n  locale: string;\r\n  max_step_num?: number;\r\n  max_search_results?: number;\r\n}\r\n\r\nexport interface KnowledgeBaseStatus {\r\n  target_article_count: number;\r\n  processed_article_count: number;\r\n  progress_percentage: number;\r\n  pending_urls: number;\r\n  processing_urls: number;\r\n  processed_urls: number;\r\n  failed_urls: number;\r\n  article_counts_by_topic: Record<string, number>;\r\n  article_counts_by_industry: Record<string, number>;\r\n  current_plan_title?: string;\r\n  plan_iterations: number;\r\n  last_error?: string;\r\n}\r\n\r\nexport interface BuildKnowledgeBaseResponse {\r\n  success: boolean;\r\n  message: string;\r\n  final_status?: KnowledgeBaseStatus;\r\n  error?: string;\r\n  execution_time_seconds?: number;\r\n}\r\n\r\nexport interface KnowledgeBaseStreamEvent {\r\n  event_type: \"status_update\" | \"error\" | \"complete\";\r\n  timestamp: string;\r\n  node?: string;\r\n  status?: KnowledgeBaseStatus;\r\n  message?: string;\r\n  error?: string;\r\n}\r\n\r\nexport async function buildKnowledgeBase(\r\n  request: BuildKnowledgeBaseRequest\r\n): Promise<BuildKnowledgeBaseResponse> {\r\n  const response = await fetch(resolveServiceURL(\"kb/build\"), {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify(request),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    throw new Error(`HTTP error! status: ${response.status}`);\r\n  }\r\n\r\n  return response.json();\r\n}\r\n\r\nexport async function* buildKnowledgeBaseStream(\r\n  request: BuildKnowledgeBaseRequest,\r\n  options: { abortSignal?: AbortSignal } = {}\r\n): AsyncIterable<KnowledgeBaseStreamEvent> {\r\n  const stream = fetchStream(resolveServiceURL(\"kb/build/stream\"), {\r\n    body: JSON.stringify(request),\r\n    signal: options.abortSignal,\r\n  });\r\n\r\n  for await (const event of stream) {\r\n    if (event.event === \"kb_update\" || event.event === \"kb_error\" || event.event === \"kb_complete\") {\r\n      yield JSON.parse(event.data) as KnowledgeBaseStreamEvent;\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAAA,yBAAyB;AACzB,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AACA;;;AA2CO,eAAe,mBACpB,OAAkC;IAElC,MAAM,WAAW,MAAM,MAAM,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa;QAC1D,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC1D;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,gBAAgB,yBACrB,OAAkC,EAClC,UAAyC,CAAC,CAAC;IAE3C,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,oBAAoB;QAC/D,MAAM,KAAK,SAAS,CAAC;QACrB,QAAQ,QAAQ,WAAW;IAC7B;IAEA,WAAW,MAAM,SAAS,OAAQ;QAChC,IAAI,MAAM,KAAK,KAAK,eAAe,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,KAAK,eAAe;YAC9F,MAAM,KAAK,KAAK,CAAC,MAAM,IAAI;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 1729, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/api/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./chat\";\r\nexport * from \"./mcp\";\r\nexport * from \"./podcast\";\r\nexport * from \"./types\";\r\nexport * from \"./kb\";"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/messages/types.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport type MessageRole = \"user\" | \"assistant\" | \"tool\";\r\n\r\nexport interface Message {\r\n  id: string;\r\n  threadId: string;\r\n  agent?:\r\n    | \"coordinator\"\r\n    | \"planner\"\r\n    | \"researcher\"\r\n    | \"coder\"\r\n    | \"reporter\"\r\n    | \"podcast\"\r\n    | \"kb_builder\"\r\n    | \"kb_planner\"\r\n    | \"kb_researcher\"\r\n    | \"kb_processor\"\r\n    | \"kb_injector\";\r\n  role: MessageRole;\r\n  isStreaming?: boolean;\r\n  content: string;\r\n  contentChunks: string[];\r\n  toolCalls?: ToolCallRuntime[];\r\n  options?: Option[];\r\n  finishReason?: \"stop\" | \"interrupt\" | \"tool_calls\";\r\n  interruptFeedback?: string;\r\n}\r\n\r\nexport interface Option {\r\n  text: string;\r\n  value: string;\r\n}\r\n\r\nexport interface ToolCallRuntime {\r\n  id: string;\r\n  name: string;\r\n  args: Record<string, unknown>;\r\n  argsChunks?: string[];\r\n  result?: string;\r\n}"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B", "debugId": null}}, {"offset": {"line": 1777, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/messages/merge-message.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport type {\r\n  ChatEvent,\r\n  InterruptEvent,\r\n  MessageChunkEvent,\r\n  ToolCallChunksEvent,\r\n  ToolCallResultEvent,\r\n  ToolCallsEvent,\r\n} from \"../api\";\r\nimport { deepClone } from \"../utils/deep-clone\";\r\n\r\nimport type { Message } from \"./types\";\r\n\r\nexport function mergeMessage(message: Message, event: ChatEvent) {\r\n  if (event.type === \"message_chunk\") {\r\n    mergeTextMessage(message, event);\r\n  } else if (event.type === \"tool_calls\" || event.type === \"tool_call_chunks\") {\r\n    mergeToolCallMessage(message, event);\r\n  } else if (event.type === \"tool_call_result\") {\r\n    mergeToolCallResultMessage(message, event);\r\n  } else if (event.type === \"interrupt\") {\r\n    mergeInterruptMessage(message, event);\r\n  }\r\n  if (event.data.finish_reason) {\r\n    message.finishReason = event.data.finish_reason;\r\n    message.isStreaming = false;\r\n    if (message.toolCalls) {\r\n      message.toolCalls.forEach((toolCall) => {\r\n        if (toolCall.argsChunks?.length) {\r\n          toolCall.args = JSON.parse(toolCall.argsChunks.join(\"\"));\r\n          delete toolCall.argsChunks;\r\n        }\r\n      });\r\n    }\r\n  }\r\n  return deepClone(message);\r\n}\r\n\r\nfunction mergeTextMessage(message: Message, event: MessageChunkEvent) {\r\n  if (event.data.content) {\r\n    message.content += event.data.content;\r\n    message.contentChunks.push(event.data.content);\r\n  }\r\n}\r\n\r\nfunction mergeToolCallMessage(\r\n  message: Message,\r\n  event: ToolCallsEvent | ToolCallChunksEvent,\r\n) {\r\n  if (event.type === \"tool_calls\" && event.data.tool_calls[0]?.name) {\r\n    message.toolCalls = event.data.tool_calls.map((raw) => ({\r\n      id: raw.id,\r\n      name: raw.name,\r\n      args: raw.args,\r\n      result: undefined,\r\n    }));\r\n  }\r\n\r\n  message.toolCalls ??= [];\r\n  for (const chunk of event.data.tool_call_chunks) {\r\n    if (chunk.id) {\r\n      const toolCall = message.toolCalls.find(\r\n        (toolCall) => toolCall.id === chunk.id,\r\n      );\r\n      if (toolCall) {\r\n        toolCall.argsChunks = [chunk.args];\r\n      }\r\n    } else {\r\n      const streamingToolCall = message.toolCalls.find(\r\n        (toolCall) => toolCall.argsChunks?.length,\r\n      );\r\n      if (streamingToolCall) {\r\n        streamingToolCall.argsChunks!.push(chunk.args);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nfunction mergeToolCallResultMessage(\r\n  message: Message,\r\n  event: ToolCallResultEvent,\r\n) {\r\n  const toolCall = message.toolCalls?.find(\r\n    (toolCall) => toolCall.id === event.data.tool_call_id,\r\n  );\r\n  if (toolCall) {\r\n    toolCall.result = event.data.content;\r\n  }\r\n}\r\n\r\nfunction mergeInterruptMessage(message: Message, event: InterruptEvent) {\r\n  message.isStreaming = false;\r\n  message.options = event.data.options;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAU/B;;AAIO,SAAS,aAAa,OAAgB,EAAE,KAAgB;IAC7D,IAAI,MAAM,IAAI,KAAK,iBAAiB;QAClC,iBAAiB,SAAS;IAC5B,OAAO,IAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,KAAK,oBAAoB;QAC3E,qBAAqB,SAAS;IAChC,OAAO,IAAI,MAAM,IAAI,KAAK,oBAAoB;QAC5C,2BAA2B,SAAS;IACtC,OAAO,IAAI,MAAM,IAAI,KAAK,aAAa;QACrC,sBAAsB,SAAS;IACjC;IACA,IAAI,MAAM,IAAI,CAAC,aAAa,EAAE;QAC5B,QAAQ,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa;QAC/C,QAAQ,WAAW,GAAG;QACtB,IAAI,QAAQ,SAAS,EAAE;YACrB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC;gBACzB,IAAI,SAAS,UAAU,EAAE,QAAQ;oBAC/B,SAAS,IAAI,GAAG,KAAK,KAAK,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC;oBACpD,OAAO,SAAS,UAAU;gBAC5B;YACF;QACF;IACF;IACA,OAAO,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE;AACnB;AAEA,SAAS,iBAAiB,OAAgB,EAAE,KAAwB;IAClE,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE;QACtB,QAAQ,OAAO,IAAI,MAAM,IAAI,CAAC,OAAO;QACrC,QAAQ,aAAa,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO;IAC/C;AACF;AAEA,SAAS,qBACP,OAAgB,EAChB,KAA2C;IAE3C,IAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM;QACjE,QAAQ,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,MAAQ,CAAC;gBACtD,IAAI,IAAI,EAAE;gBACV,MAAM,IAAI,IAAI;gBACd,MAAM,IAAI,IAAI;gBACd,QAAQ;YACV,CAAC;IACH;IAEA,QAAQ,SAAS,KAAK,EAAE;IACxB,KAAK,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB,CAAE;QAC/C,IAAI,MAAM,EAAE,EAAE;YACZ,MAAM,WAAW,QAAQ,SAAS,CAAC,IAAI,CACrC,CAAC,WAAa,SAAS,EAAE,KAAK,MAAM,EAAE;YAExC,IAAI,UAAU;gBACZ,SAAS,UAAU,GAAG;oBAAC,MAAM,IAAI;iBAAC;YACpC;QACF,OAAO;YACL,MAAM,oBAAoB,QAAQ,SAAS,CAAC,IAAI,CAC9C,CAAC,WAAa,SAAS,UAAU,EAAE;YAErC,IAAI,mBAAmB;gBACrB,kBAAkB,UAAU,CAAE,IAAI,CAAC,MAAM,IAAI;YAC/C;QACF;IACF;AACF;AAEA,SAAS,2BACP,OAAgB,EAChB,KAA0B;IAE1B,MAAM,WAAW,QAAQ,SAAS,EAAE,KAClC,CAAC,WAAa,SAAS,EAAE,KAAK,MAAM,IAAI,CAAC,YAAY;IAEvD,IAAI,UAAU;QACZ,SAAS,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO;IACtC;AACF;AAEA,SAAS,sBAAsB,OAAgB,EAAE,KAAqB;IACpE,QAAQ,WAAW,GAAG;IACtB,QAAQ,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO;AACtC", "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/messages/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./types\";\r\nexport * from \"./merge-message\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B;AACA", "debugId": null}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/store/settings-store.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { create } from \"zustand\";\r\n\r\nimport type { MCPServerMetadata, SimpleMCPServerMetadata } from \"../mcp\";\r\n\r\nconst SETTINGS_KEY = \"deerflow.settings\";\r\n\r\nconst DEFAULT_SETTINGS: SettingsState = {\r\n  general: {\r\n    autoAcceptedPlan: false,\r\n    enableBackgroundInvestigation: false,\r\n    maxPlanIterations: 1,\r\n    maxStepNum: 3,\r\n    maxSearchResults: 3,\r\n  },\r\n  mcp: {\r\n    servers: [],\r\n  },\r\n};\r\n\r\nexport type SettingsState = {\r\n  general: {\r\n    autoAcceptedPlan: boolean;\r\n    enableBackgroundInvestigation: boolean;\r\n    maxPlanIterations: number;\r\n    maxStepNum: number;\r\n    maxSearchResults: number;\r\n  };\r\n  mcp: {\r\n    servers: MCPServerMetadata[];\r\n  };\r\n};\r\n\r\nexport const useSettingsStore = create<SettingsState>(() => ({\r\n  ...DEFAULT_SETTINGS,\r\n}));\r\n\r\nexport const useSettings = (key: keyof SettingsState) => {\r\n  return useSettingsStore((state) => state[key]);\r\n};\r\n\r\nexport const changeSettings = (settings: SettingsState) => {\r\n  useSettingsStore.setState(settings);\r\n};\r\n\r\nexport const loadSettings = () => {\r\n  if (typeof window === \"undefined\") {\r\n    return;\r\n  }\r\n  const json = localStorage.getItem(SETTINGS_KEY);\r\n  if (json) {\r\n    const settings = JSON.parse(json);\r\n    for (const key in DEFAULT_SETTINGS.general) {\r\n      if (!(key in settings.general)) {\r\n        settings.general[key as keyof SettingsState[\"general\"]] =\r\n          DEFAULT_SETTINGS.general[key as keyof SettingsState[\"general\"]];\r\n      }\r\n    }\r\n\r\n    try {\r\n      useSettingsStore.setState(settings);\r\n    } catch (error) {\r\n      console.error(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport const saveSettings = () => {\r\n  const latestSettings = useSettingsStore.getState();\r\n  const json = JSON.stringify(latestSettings);\r\n  localStorage.setItem(SETTINGS_KEY, json);\r\n};\r\n\r\nexport const getChatStreamSettings = () => {\r\n  let mcpSettings:\r\n    | {\r\n        servers: Record<\r\n          string,\r\n          MCPServerMetadata & {\r\n            enabled_tools: string[];\r\n            add_to_agents: string[];\r\n          }\r\n        >;\r\n      }\r\n    | undefined = undefined;\r\n  const { mcp, general } = useSettingsStore.getState();\r\n  const mcpServers = mcp.servers.filter((server) => server.enabled);\r\n  if (mcpServers.length > 0) {\r\n    mcpSettings = {\r\n      servers: mcpServers.reduce((acc, cur) => {\r\n        const { transport, env } = cur;\r\n        let server: SimpleMCPServerMetadata;\r\n        if (transport === \"stdio\") {\r\n          server = {\r\n            name: cur.name,\r\n            transport,\r\n            env,\r\n            command: cur.command,\r\n            args: cur.args,\r\n          };\r\n        } else {\r\n          server = {\r\n            name: cur.name,\r\n            transport,\r\n            env,\r\n            url: cur.url,\r\n          };\r\n        }\r\n        return {\r\n          ...acc,\r\n          [cur.name]: {\r\n            ...server,\r\n            enabled_tools: cur.tools.map((tool) => tool.name),\r\n            add_to_agents: [\"researcher\"],\r\n          },\r\n        };\r\n      }, {}),\r\n    };\r\n  }\r\n  return {\r\n    ...general,\r\n    mcpSettings,\r\n  };\r\n};\r\n\r\nexport function setEnableBackgroundInvestigation(value: boolean) {\r\n  useSettingsStore.setState((state) => ({\r\n    general: {\r\n      ...state.general,\r\n      enableBackgroundInvestigation: value,\r\n    },\r\n  }));\r\n  saveSettings();\r\n}\r\nloadSettings();\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;;;AAE/B;;;AAIA,MAAM,eAAe;AAErB,MAAM,mBAAkC;IACtC,SAAS;QACP,kBAAkB;QAClB,+BAA+B;QAC/B,mBAAmB;QACnB,YAAY;QACZ,kBAAkB;IACpB;IACA,KAAK;QACH,SAAS,EAAE;IACb;AACF;AAeO,MAAM,mBAAmB,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,EAAiB,IAAM,CAAC;QAC3D,GAAG,gBAAgB;IACrB,CAAC;AAEM,MAAM,cAAc,CAAC;;IAC1B,OAAO;wCAAiB,CAAC,QAAU,KAAK,CAAC,IAAI;;AAC/C;GAFa;;QACJ;;;AAGF,MAAM,iBAAiB,CAAC;IAC7B,iBAAiB,QAAQ,CAAC;AAC5B;AAEO,MAAM,eAAe;IAC1B,uCAAmC;;IAEnC;IACA,MAAM,OAAO,aAAa,OAAO,CAAC;IAClC,IAAI,MAAM;QACR,MAAM,WAAW,KAAK,KAAK,CAAC;QAC5B,IAAK,MAAM,OAAO,iBAAiB,OAAO,CAAE;YAC1C,IAAI,CAAC,CAAC,OAAO,SAAS,OAAO,GAAG;gBAC9B,SAAS,OAAO,CAAC,IAAsC,GACrD,iBAAiB,OAAO,CAAC,IAAsC;YACnE;QACF;QAEA,IAAI;YACF,iBAAiB,QAAQ,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;AACF;AAEO,MAAM,eAAe;IAC1B,MAAM,iBAAiB,iBAAiB,QAAQ;IAChD,MAAM,OAAO,KAAK,SAAS,CAAC;IAC5B,aAAa,OAAO,CAAC,cAAc;AACrC;AAEO,MAAM,wBAAwB;IACnC,IAAI,cAUY;IAChB,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,iBAAiB,QAAQ;IAClD,MAAM,aAAa,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,SAAW,OAAO,OAAO;IAChE,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,cAAc;YACZ,SAAS,WAAW,MAAM,CAAC,CAAC,KAAK;gBAC/B,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG;gBAC3B,IAAI;gBACJ,IAAI,cAAc,SAAS;oBACzB,SAAS;wBACP,MAAM,IAAI,IAAI;wBACd;wBACA;wBACA,SAAS,IAAI,OAAO;wBACpB,MAAM,IAAI,IAAI;oBAChB;gBACF,OAAO;oBACL,SAAS;wBACP,MAAM,IAAI,IAAI;wBACd;wBACA;wBACA,KAAK,IAAI,GAAG;oBACd;gBACF;gBACA,OAAO;oBACL,GAAG,GAAG;oBACN,CAAC,IAAI,IAAI,CAAC,EAAE;wBACV,GAAG,MAAM;wBACT,eAAe,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;wBAChD,eAAe;4BAAC;yBAAa;oBAC/B;gBACF;YACF,GAAG,CAAC;QACN;IACF;IACA,OAAO;QACL,GAAG,OAAO;QACV;IACF;AACF;AAEO,SAAS,iCAAiC,KAAc;IAC7D,iBAAiB,QAAQ,CAAC,CAAC,QAAU,CAAC;YACpC,SAAS;gBACP,GAAG,MAAM,OAAO;gBAChB,+BAA+B;YACjC;QACF,CAAC;IACD;AACF;AACA", "debugId": null}}, {"offset": {"line": 2015, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/store/store.ts"], "sourcesContent": ["// web/src/core/store/store.ts\r\n// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { nanoid } from \"nanoid\";\r\nimport { toast } from \"sonner\";\r\nimport { create } from \"zustand\";\r\nimport { useShallow } from \"zustand/react/shallow\";\r\n\r\nimport { chatStream, generatePodcast } from \"../api\";\r\nimport { buildKnowledgeBaseStream, type BuildKnowledgeBaseRequest, type KnowledgeBaseStatus } from \"../api/kb\";\r\nimport type { Message } from \"../messages\";\r\nimport { mergeMessage } from \"../messages\";\r\nimport { parseJSON } from \"../utils\";\r\n\r\nimport { getChatStreamSettings } from \"./settings-store\";\r\n\r\nconst THREAD_ID = nanoid();\r\n\r\n// Knowledge Base Types\r\nexport interface KnowledgeBaseState {\r\n  isBuilding: boolean;\r\n  currentRequest: BuildKnowledgeBaseRequest | null;\r\n  status: KnowledgeBaseStatus | null;\r\n  lastError: string | null;\r\n  buildHistory: KnowledgeBaseBuild[];\r\n}\r\n\r\nexport interface KnowledgeBaseBuild {\r\n  id: string;\r\n  request: BuildKnowledgeBaseRequest;\r\n  status: KnowledgeBaseStatus;\r\n  startTime: string;\r\n  endTime?: string;\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\nexport const useStore = create<{\r\n  responding: boolean;\r\n  threadId: string | undefined;\r\n  messageIds: string[];\r\n  messages: Map<string, Message>;\r\n  researchIds: string[];\r\n  researchPlanIds: Map<string, string>;\r\n  researchReportIds: Map<string, string>;\r\n  researchActivityIds: Map<string, string[]>;\r\n  ongoingResearchId: string | null;\r\n  openResearchId: string | null;\r\n\r\n  // Knowledge Base state\r\n  knowledgeBase: KnowledgeBaseState;\r\n\r\n  appendMessage: (message: Message) => void;\r\n  updateMessage: (message: Message) => void;\r\n  updateMessages: (messages: Message[]) => void;\r\n  openResearch: (researchId: string | null) => void;\r\n  closeResearch: () => void;\r\n  setOngoingResearch: (researchId: string | null) => void;\r\n\r\n  // Knowledge Base actions\r\n  startKnowledgeBaseBuild: (request: BuildKnowledgeBaseRequest) => Promise<void>;\r\n  stopKnowledgeBaseBuild: () => void;\r\n  clearKnowledgeBaseError: () => void;\r\n}>((set, get) => ({\r\n  responding: false,\r\n  threadId: THREAD_ID,\r\n  messageIds: [],\r\n  messages: new Map<string, Message>(),\r\n  researchIds: [],\r\n  researchPlanIds: new Map<string, string>(),\r\n  researchReportIds: new Map<string, string>(),\r\n  researchActivityIds: new Map<string, string[]>(),\r\n  ongoingResearchId: null,\r\n  openResearchId: null,\r\n\r\n  // Knowledge Base initial state\r\n  knowledgeBase: {\r\n    isBuilding: false,\r\n    currentRequest: null,\r\n    status: null,\r\n    lastError: null,\r\n    buildHistory: [],\r\n  },\r\n\r\n  appendMessage(message: Message) {\r\n    set((state) => ({\r\n      messageIds: [...state.messageIds, message.id],\r\n      messages: new Map(state.messages).set(message.id, message),\r\n    }));\r\n  },\r\n  updateMessage(message: Message) {\r\n    set((state) => ({\r\n      messages: new Map(state.messages).set(message.id, message),\r\n    }));\r\n  },\r\n  updateMessages(messages: Message[]) {\r\n    set((state) => {\r\n      const newMessages = new Map(state.messages);\r\n      messages.forEach((m) => newMessages.set(m.id, m));\r\n      return { messages: newMessages };\r\n    });\r\n  },\r\n  openResearch(researchId: string | null) {\r\n    set({ openResearchId: researchId });\r\n  },\r\n  closeResearch() {\r\n    set({ openResearchId: null });\r\n  },\r\n  setOngoingResearch(researchId: string | null) {\r\n    set({ ongoingResearchId: researchId });\r\n  },\r\n\r\n  // Knowledge Base Actions\r\n  async startKnowledgeBaseBuild(request: BuildKnowledgeBaseRequest) {\r\n    const buildId = nanoid();\r\n    const startTime = new Date().toISOString();\r\n    \r\n    set((state) => ({\r\n      knowledgeBase: {\r\n        ...state.knowledgeBase,\r\n        isBuilding: true,\r\n        currentRequest: request,\r\n        status: null,\r\n        lastError: null,\r\n      },\r\n    }));\r\n\r\n    // Add user message about KB building\r\n    appendMessage({\r\n      id: nanoid(),\r\n      threadId: THREAD_ID,\r\n      role: \"user\",\r\n      content: `Build knowledge base: ${request.topics.join(\", \")} (${request.target_article_count} articles)`,\r\n      contentChunks: [],\r\n    });\r\n\r\n    // Add KB building status message\r\n    const kbMessageId = nanoid();\r\n    const kbMessage: Message = {\r\n      id: kbMessageId,\r\n      threadId: THREAD_ID,\r\n      role: \"assistant\",\r\n      agent: \"kb_builder\",\r\n      content: JSON.stringify({\r\n        type: \"kb_building\",\r\n        request,\r\n        buildId,\r\n        status: \"starting\"\r\n      }),\r\n      contentChunks: [],\r\n      isStreaming: true,\r\n    };\r\n    appendMessage(kbMessage);\r\n\r\n    try {\r\n      const abortController = new AbortController();\r\n      \r\n      // Store abort controller for potential cancellation\r\n      (get() as any)._kbAbortController = abortController;\r\n\r\n      const stream = buildKnowledgeBaseStream(request, {\r\n        abortSignal: abortController.signal,\r\n      });\r\n\r\n      for await (const event of stream) {\r\n        if (event.event_type === \"status_update\" && event.status) {\r\n          set((state) => ({\r\n            knowledgeBase: {\r\n              ...state.knowledgeBase,\r\n              status: event.status!,\r\n            },\r\n          }));\r\n\r\n          // Update KB message with current status\r\n          const currentMessage = get().messages.get(kbMessageId);\r\n          if (currentMessage) {\r\n            const updatedContent = JSON.stringify({\r\n              type: \"kb_building\",\r\n              request,\r\n              buildId,\r\n              status: \"building\",\r\n              currentStatus: event.status,\r\n              message: event.message,\r\n            });\r\n            \r\n            set((state) => ({\r\n              messages: new Map(state.messages).set(kbMessageId, {\r\n                ...currentMessage,\r\n                content: updatedContent,\r\n              }),\r\n            }));\r\n          }\r\n        } else if (event.event_type === \"error\") {\r\n          const errorMsg = event.error || \"Unknown error occurred\";\r\n          set((state) => ({\r\n            knowledgeBase: {\r\n              ...state.knowledgeBase,\r\n              isBuilding: false,\r\n              lastError: errorMsg,\r\n            },\r\n          }));\r\n\r\n          // Update KB message with error\r\n          const currentMessage = get().messages.get(kbMessageId);\r\n          if (currentMessage) {\r\n            const updatedContent = JSON.stringify({\r\n              type: \"kb_building\",\r\n              request,\r\n              buildId,\r\n              status: \"error\",\r\n              error: errorMsg,\r\n            });\r\n            \r\n            set((state) => ({\r\n              messages: new Map(state.messages).set(kbMessageId, {\r\n                ...currentMessage,\r\n                content: updatedContent,\r\n                isStreaming: false,\r\n              }),\r\n            }));\r\n          }\r\n          \r\n          toast(`Knowledge base building failed: ${errorMsg}`);\r\n          break;\r\n        } else if (event.event_type === \"complete\") {\r\n          const finalStatus = get().knowledgeBase.status;\r\n          const endTime = new Date().toISOString();\r\n          \r\n          // Add to build history\r\n          const build: KnowledgeBaseBuild = {\r\n            id: buildId,\r\n            request,\r\n            status: finalStatus!,\r\n            startTime,\r\n            endTime,\r\n            success: true,\r\n          };\r\n\r\n          set((state) => ({\r\n            knowledgeBase: {\r\n              ...state.knowledgeBase,\r\n              isBuilding: false,\r\n              buildHistory: [...state.knowledgeBase.buildHistory, build],\r\n            },\r\n          }));\r\n\r\n          // Update KB message with completion\r\n          const currentMessage = get().messages.get(kbMessageId);\r\n          if (currentMessage) {\r\n            const updatedContent = JSON.stringify({\r\n              type: \"kb_building\",\r\n              request,\r\n              buildId,\r\n              status: \"complete\",\r\n              finalStatus,\r\n              executionTime: endTime,\r\n            });\r\n            \r\n            set((state) => ({\r\n              messages: new Map(state.messages).set(kbMessageId, {\r\n                ...currentMessage,\r\n                content: updatedContent,\r\n                isStreaming: false,\r\n              }),\r\n            }));\r\n          }\r\n\r\n          toast(`Knowledge base built successfully! Acquired ${finalStatus?.processed_article_count} articles.`);\r\n          break;\r\n        }\r\n      }\r\n    } catch (error: any) {\r\n      if (error.name === \"AbortError\") {\r\n        // Build was cancelled\r\n        set((state) => ({\r\n          knowledgeBase: {\r\n            ...state.knowledgeBase,\r\n            isBuilding: false,\r\n            lastError: \"Build cancelled by user\",\r\n          },\r\n        }));\r\n\r\n        const currentMessage = get().messages.get(kbMessageId);\r\n        if (currentMessage) {\r\n          const updatedContent = JSON.stringify({\r\n            type: \"kb_building\",\r\n            request,\r\n            buildId,\r\n            status: \"cancelled\",\r\n          });\r\n          \r\n          set((state) => ({\r\n            messages: new Map(state.messages).set(kbMessageId, {\r\n              ...currentMessage,\r\n              content: updatedContent,\r\n              isStreaming: false,\r\n            }),\r\n          }));\r\n        }\r\n        \r\n        toast(\"Knowledge base building cancelled\");\r\n      } else {\r\n        const errorMsg = error.message || \"Unknown error occurred\";\r\n        set((state) => ({\r\n          knowledgeBase: {\r\n            ...state.knowledgeBase,\r\n            isBuilding: false,\r\n            lastError: errorMsg,\r\n          },\r\n        }));\r\n\r\n        const currentMessage = get().messages.get(kbMessageId);\r\n        if (currentMessage) {\r\n          const updatedContent = JSON.stringify({\r\n            type: \"kb_building\",\r\n            request,\r\n            buildId,\r\n            status: \"error\",\r\n            error: errorMsg,\r\n          });\r\n          \r\n          set((state) => ({\r\n            messages: new Map(state.messages).set(kbMessageId, {\r\n              ...currentMessage,\r\n              content: updatedContent,\r\n              isStreaming: false,\r\n            }),\r\n          }));\r\n        }\r\n        \r\n        toast(`Knowledge base building error: ${errorMsg}`);\r\n      }\r\n    } finally {\r\n      // Clean up abort controller\r\n      delete (get() as any)._kbAbortController;\r\n    }\r\n  },\r\n\r\n  stopKnowledgeBaseBuild() {\r\n    const abortController = (get() as any)._kbAbortController;\r\n    if (abortController) {\r\n      abortController.abort();\r\n    }\r\n  },\r\n\r\n  clearKnowledgeBaseError() {\r\n    set((state) => ({\r\n      knowledgeBase: {\r\n        ...state.knowledgeBase,\r\n        lastError: null,\r\n      },\r\n    }));\r\n  },\r\n}));\r\n\r\nexport async function sendMessage(\r\n  content?: string,\r\n  {\r\n    interruptFeedback,\r\n  }: {\r\n    interruptFeedback?: string;\r\n  } = {},\r\n  options: { abortSignal?: AbortSignal } = {},\r\n) {\r\n  if (content != null) {\r\n    appendMessage({\r\n      id: nanoid(),\r\n      threadId: THREAD_ID,\r\n      role: \"user\",\r\n      content: content,\r\n      contentChunks: [content],\r\n    });\r\n  }\r\n\r\n  const settings = getChatStreamSettings();\r\n  const stream = chatStream(\r\n    content ?? \"[REPLAY]\",\r\n    {\r\n      thread_id: THREAD_ID,\r\n      interrupt_feedback: interruptFeedback,\r\n      auto_accepted_plan: settings.autoAcceptedPlan,\r\n      enable_background_investigation:\r\n        settings.enableBackgroundInvestigation ?? true,\r\n      max_plan_iterations: settings.maxPlanIterations,\r\n      max_step_num: settings.maxStepNum,\r\n      max_search_results: settings.maxSearchResults,\r\n      mcp_settings: settings.mcpSettings,\r\n    },\r\n    options,\r\n  );\r\n\r\n  setResponding(true);\r\n  let messageId: string | undefined;\r\n  try {\r\n    for await (const event of stream) {\r\n      const { type, data } = event;\r\n      messageId = data.id;\r\n      let message: Message | undefined;\r\n      if (type === \"tool_call_result\") {\r\n        message = findMessageByToolCallId(data.tool_call_id);\r\n      } else if (!existsMessage(messageId)) {\r\n        message = {\r\n          id: messageId,\r\n          threadId: data.thread_id,\r\n          agent: data.agent,\r\n          role: data.role,\r\n          content: \"\",\r\n          contentChunks: [],\r\n          isStreaming: true,\r\n          interruptFeedback,\r\n        };\r\n        appendMessage(message);\r\n      }\r\n      message ??= getMessage(messageId);\r\n      if (message) {\r\n        message = mergeMessage(message, event);\r\n        updateMessage(message);\r\n      }\r\n    }\r\n  } catch {\r\n    toast(\"An error occurred while generating the response. Please try again.\");\r\n    // Update message status.\r\n    // TODO: const isAborted = (error as Error).name === \"AbortError\";\r\n    if (messageId != null) {\r\n      const message = getMessage(messageId);\r\n      if (message?.isStreaming) {\r\n        message.isStreaming = false;\r\n        useStore.getState().updateMessage(message);\r\n      }\r\n    }\r\n    useStore.getState().setOngoingResearch(null);\r\n  } finally {\r\n    setResponding(false);\r\n  }\r\n}\r\n\r\nfunction setResponding(value: boolean) {\r\n  useStore.setState({ responding: value });\r\n}\r\n\r\nfunction existsMessage(id: string) {\r\n  return useStore.getState().messageIds.includes(id);\r\n}\r\n\r\nfunction getMessage(id: string) {\r\n  return useStore.getState().messages.get(id);\r\n}\r\n\r\nfunction findMessageByToolCallId(toolCallId: string) {\r\n  return Array.from(useStore.getState().messages.values())\r\n    .reverse()\r\n    .find((message) => {\r\n      if (message.toolCalls) {\r\n        return message.toolCalls.some((toolCall) => toolCall.id === toolCallId);\r\n      }\r\n      return false;\r\n    });\r\n}\r\n\r\nfunction appendMessage(message: Message) {\r\n  if (\r\n    message.agent === \"coder\" ||\r\n    message.agent === \"reporter\" ||\r\n    message.agent === \"researcher\"\r\n  ) {\r\n    if (!getOngoingResearchId()) {\r\n      const id = message.id;\r\n      appendResearch(id);\r\n      openResearch(id);\r\n    }\r\n    appendResearchActivity(message);\r\n  }\r\n  useStore.getState().appendMessage(message);\r\n}\r\n\r\nfunction updateMessage(message: Message) {\r\n  if (\r\n    getOngoingResearchId() &&\r\n    message.agent === \"reporter\" &&\r\n    !message.isStreaming\r\n  ) {\r\n    useStore.getState().setOngoingResearch(null);\r\n  }\r\n  useStore.getState().updateMessage(message);\r\n}\r\n\r\nfunction getOngoingResearchId() {\r\n  return useStore.getState().ongoingResearchId;\r\n}\r\n\r\nfunction appendResearch(researchId: string) {\r\n  let planMessage: Message | undefined;\r\n  const reversedMessageIds = [...useStore.getState().messageIds].reverse();\r\n  for (const messageId of reversedMessageIds) {\r\n    const message = getMessage(messageId);\r\n    if (message?.agent === \"planner\") {\r\n      planMessage = message;\r\n      break;\r\n    }\r\n  }\r\n  const messageIds = [researchId];\r\n  messageIds.unshift(planMessage!.id);\r\n  useStore.setState({\r\n    ongoingResearchId: researchId,\r\n    researchIds: [...useStore.getState().researchIds, researchId],\r\n    researchPlanIds: new Map(useStore.getState().researchPlanIds).set(\r\n      researchId,\r\n      planMessage!.id,\r\n    ),\r\n    researchActivityIds: new Map(useStore.getState().researchActivityIds).set(\r\n      researchId,\r\n      messageIds,\r\n    ),\r\n  });\r\n}\r\n\r\nfunction appendResearchActivity(message: Message) {\r\n  const researchId = getOngoingResearchId();\r\n  if (researchId) {\r\n    const researchActivityIds = useStore.getState().researchActivityIds;\r\n    const current = researchActivityIds.get(researchId)!;\r\n    if (!current.includes(message.id)) {\r\n      useStore.setState({\r\n        researchActivityIds: new Map(researchActivityIds).set(researchId, [\r\n          ...current,\r\n          message.id,\r\n        ]),\r\n      });\r\n    }\r\n    if (message.agent === \"reporter\") {\r\n      useStore.setState({\r\n        researchReportIds: new Map(useStore.getState().researchReportIds).set(\r\n          researchId,\r\n          message.id,\r\n        ),\r\n      });\r\n    }\r\n  }\r\n}\r\n\r\nexport function openResearch(researchId: string | null) {\r\n  useStore.getState().openResearch(researchId);\r\n}\r\n\r\nexport function closeResearch() {\r\n  useStore.getState().closeResearch();\r\n}\r\n\r\nexport async function listenToPodcast(researchId: string) {\r\n  const planMessageId = useStore.getState().researchPlanIds.get(researchId);\r\n  const reportMessageId = useStore.getState().researchReportIds.get(researchId);\r\n  if (planMessageId && reportMessageId) {\r\n    const planMessage = getMessage(planMessageId)!;\r\n    const title = parseJSON(planMessage.content, { title: \"Untitled\" }).title;\r\n    const reportMessage = getMessage(reportMessageId);\r\n    if (reportMessage?.content) {\r\n      appendMessage({\r\n        id: nanoid(),\r\n        threadId: THREAD_ID,\r\n        role: \"user\",\r\n        content: \"Please generate a podcast for the above research.\",\r\n        contentChunks: [],\r\n      });\r\n      const podCastMessageId = nanoid();\r\n      const podcastObject = { title, researchId };\r\n      const podcastMessage: Message = {\r\n        id: podCastMessageId,\r\n        threadId: THREAD_ID,\r\n        role: \"assistant\",\r\n        agent: \"podcast\",\r\n        content: JSON.stringify(podcastObject),\r\n        contentChunks: [],\r\n        isStreaming: true,\r\n      };\r\n      appendMessage(podcastMessage);\r\n      // Generating podcast...\r\n      let audioUrl: string | undefined;\r\n      try {\r\n        audioUrl = await generatePodcast(reportMessage.content);\r\n      } catch (e) {\r\n        console.error(e);\r\n        useStore.setState((state) => ({\r\n          messages: new Map(useStore.getState().messages).set(\r\n            podCastMessageId,\r\n            {\r\n              ...state.messages.get(podCastMessageId)!,\r\n              content: JSON.stringify({\r\n                ...podcastObject,\r\n                error: e instanceof Error ? e.message : \"Unknown error\",\r\n              }),\r\n              isStreaming: false,\r\n            },\r\n          ),\r\n        }));\r\n        toast(\"An error occurred while generating podcast. Please try again.\");\r\n        return;\r\n      }\r\n      useStore.setState((state) => ({\r\n        messages: new Map(useStore.getState().messages).set(podCastMessageId, {\r\n          ...state.messages.get(podCastMessageId)!,\r\n          content: JSON.stringify({ ...podcastObject, audioUrl }),\r\n          isStreaming: false,\r\n        }),\r\n      }));\r\n    }\r\n  }\r\n}\r\n\r\nexport function useResearchMessage(researchId: string) {\r\n  return useStore(\r\n    useShallow((state) => {\r\n      const messageId = state.researchPlanIds.get(researchId);\r\n      return messageId ? state.messages.get(messageId) : undefined;\r\n    }),\r\n  );\r\n}\r\n\r\nexport function useMessage(messageId: string | null | undefined) {\r\n  return useStore(\r\n    useShallow((state) =>\r\n      messageId ? state.messages.get(messageId) : undefined,\r\n    ),\r\n  );\r\n}\r\n\r\nexport function useMessageIds() {\r\n  return useStore(useShallow((state) => state.messageIds));\r\n}\r\n\r\nexport function useLastInterruptMessage() {\r\n  return useStore(\r\n    useShallow((state) => {\r\n      if (state.messageIds.length >= 2) {\r\n        const lastMessage = state.messages.get(\r\n          state.messageIds[state.messageIds.length - 1]!,\r\n        );\r\n        return lastMessage?.finishReason === \"interrupt\" ? lastMessage : null;\r\n      }\r\n      return null;\r\n    }),\r\n  );\r\n}\r\n\r\nexport function useLastFeedbackMessageId() {\r\n  const waitingForFeedbackMessageId = useStore(\r\n    useShallow((state) => {\r\n      if (state.messageIds.length >= 2) {\r\n        const lastMessage = state.messages.get(\r\n          state.messageIds[state.messageIds.length - 1]!,\r\n        );\r\n        if (lastMessage && lastMessage.finishReason === \"interrupt\") {\r\n          return state.messageIds[state.messageIds.length - 2];\r\n        }\r\n      }\r\n      return null;\r\n    }),\r\n  );\r\n  return waitingForFeedbackMessageId;\r\n}\r\n\r\nexport function useToolCalls() {\r\n  return useStore(\r\n    useShallow((state) => {\r\n      return state.messageIds\r\n        ?.map((id) => getMessage(id)?.toolCalls)\r\n        .filter((toolCalls) => toolCalls != null)\r\n        .flat();\r\n    }),\r\n  );\r\n}\r\n\r\n// Knowledge Base hooks\r\nexport function useKnowledgeBaseState() {\r\n  return useStore((state) => state.knowledgeBase);\r\n}\r\n\r\nexport function useKnowledgeBaseActions() {\r\n  return useStore((state) => ({\r\n    startBuild: state.startKnowledgeBaseBuild,\r\n    stopBuild: state.stopKnowledgeBaseBuild,\r\n    clearError: state.clearKnowledgeBaseError,\r\n  }));\r\n}"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,0DAA0D;AAC1D,+BAA+B;;;;;;;;;;;;;;;;AAE/B;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAEA;;;;;;;;;;;AAEA,MAAM,YAAY,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;AAqBhB,MAAM,WAAW,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,EA0B1B,CAAC,KAAK,MAAQ,CAAC;QAChB,YAAY;QACZ,UAAU;QACV,YAAY,EAAE;QACd,UAAU,IAAI;QACd,aAAa,EAAE;QACf,iBAAiB,IAAI;QACrB,mBAAmB,IAAI;QACvB,qBAAqB,IAAI;QACzB,mBAAmB;QACnB,gBAAgB;QAEhB,+BAA+B;QAC/B,eAAe;YACb,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,WAAW;YACX,cAAc,EAAE;QAClB;QAEA,eAAc,OAAgB;YAC5B,IAAI,CAAC,QAAU,CAAC;oBACd,YAAY;2BAAI,MAAM,UAAU;wBAAE,QAAQ,EAAE;qBAAC;oBAC7C,UAAU,IAAI,IAAI,MAAM,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE;gBACpD,CAAC;QACH;QACA,eAAc,OAAgB;YAC5B,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,IAAI,IAAI,MAAM,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE;gBACpD,CAAC;QACH;QACA,gBAAe,QAAmB;YAChC,IAAI,CAAC;gBACH,MAAM,cAAc,IAAI,IAAI,MAAM,QAAQ;gBAC1C,SAAS,OAAO,CAAC,CAAC,IAAM,YAAY,GAAG,CAAC,EAAE,EAAE,EAAE;gBAC9C,OAAO;oBAAE,UAAU;gBAAY;YACjC;QACF;QACA,cAAa,UAAyB;YACpC,IAAI;gBAAE,gBAAgB;YAAW;QACnC;QACA;YACE,IAAI;gBAAE,gBAAgB;YAAK;QAC7B;QACA,oBAAmB,UAAyB;YAC1C,IAAI;gBAAE,mBAAmB;YAAW;QACtC;QAEA,yBAAyB;QACzB,MAAM,yBAAwB,OAAkC;YAC9D,MAAM,UAAU,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;YACrB,MAAM,YAAY,IAAI,OAAO,WAAW;YAExC,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,YAAY;wBACZ,gBAAgB;wBAChB,QAAQ;wBACR,WAAW;oBACb;gBACF,CAAC;YAED,qCAAqC;YACrC,cAAc;gBACZ,IAAI,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;gBACT,UAAU;gBACV,MAAM;gBACN,SAAS,CAAC,sBAAsB,EAAE,QAAQ,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,QAAQ,oBAAoB,CAAC,UAAU,CAAC;gBACxG,eAAe,EAAE;YACnB;YAEA,iCAAiC;YACjC,MAAM,cAAc,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;YACzB,MAAM,YAAqB;gBACzB,IAAI;gBACJ,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS,KAAK,SAAS,CAAC;oBACtB,MAAM;oBACN;oBACA;oBACA,QAAQ;gBACV;gBACA,eAAe,EAAE;gBACjB,aAAa;YACf;YACA,cAAc;YAEd,IAAI;gBACF,MAAM,kBAAkB,IAAI;gBAE5B,oDAAoD;gBACnD,MAAc,kBAAkB,GAAG;gBAEpC,MAAM,SAAS,CAAA,GAAA,2HAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS;oBAC/C,aAAa,gBAAgB,MAAM;gBACrC;gBAEA,WAAW,MAAM,SAAS,OAAQ;oBAChC,IAAI,MAAM,UAAU,KAAK,mBAAmB,MAAM,MAAM,EAAE;wBACxD,IAAI,CAAC,QAAU,CAAC;gCACd,eAAe;oCACb,GAAG,MAAM,aAAa;oCACtB,QAAQ,MAAM,MAAM;gCACtB;4BACF,CAAC;wBAED,wCAAwC;wBACxC,MAAM,iBAAiB,MAAM,QAAQ,CAAC,GAAG,CAAC;wBAC1C,IAAI,gBAAgB;4BAClB,MAAM,iBAAiB,KAAK,SAAS,CAAC;gCACpC,MAAM;gCACN;gCACA;gCACA,QAAQ;gCACR,eAAe,MAAM,MAAM;gCAC3B,SAAS,MAAM,OAAO;4BACxB;4BAEA,IAAI,CAAC,QAAU,CAAC;oCACd,UAAU,IAAI,IAAI,MAAM,QAAQ,EAAE,GAAG,CAAC,aAAa;wCACjD,GAAG,cAAc;wCACjB,SAAS;oCACX;gCACF,CAAC;wBACH;oBACF,OAAO,IAAI,MAAM,UAAU,KAAK,SAAS;wBACvC,MAAM,WAAW,MAAM,KAAK,IAAI;wBAChC,IAAI,CAAC,QAAU,CAAC;gCACd,eAAe;oCACb,GAAG,MAAM,aAAa;oCACtB,YAAY;oCACZ,WAAW;gCACb;4BACF,CAAC;wBAED,+BAA+B;wBAC/B,MAAM,iBAAiB,MAAM,QAAQ,CAAC,GAAG,CAAC;wBAC1C,IAAI,gBAAgB;4BAClB,MAAM,iBAAiB,KAAK,SAAS,CAAC;gCACpC,MAAM;gCACN;gCACA;gCACA,QAAQ;gCACR,OAAO;4BACT;4BAEA,IAAI,CAAC,QAAU,CAAC;oCACd,UAAU,IAAI,IAAI,MAAM,QAAQ,EAAE,GAAG,CAAC,aAAa;wCACjD,GAAG,cAAc;wCACjB,SAAS;wCACT,aAAa;oCACf;gCACF,CAAC;wBACH;wBAEA,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,CAAC,gCAAgC,EAAE,UAAU;wBACnD;oBACF,OAAO,IAAI,MAAM,UAAU,KAAK,YAAY;wBAC1C,MAAM,cAAc,MAAM,aAAa,CAAC,MAAM;wBAC9C,MAAM,UAAU,IAAI,OAAO,WAAW;wBAEtC,uBAAuB;wBACvB,MAAM,QAA4B;4BAChC,IAAI;4BACJ;4BACA,QAAQ;4BACR;4BACA;4BACA,SAAS;wBACX;wBAEA,IAAI,CAAC,QAAU,CAAC;gCACd,eAAe;oCACb,GAAG,MAAM,aAAa;oCACtB,YAAY;oCACZ,cAAc;2CAAI,MAAM,aAAa,CAAC,YAAY;wCAAE;qCAAM;gCAC5D;4BACF,CAAC;wBAED,oCAAoC;wBACpC,MAAM,iBAAiB,MAAM,QAAQ,CAAC,GAAG,CAAC;wBAC1C,IAAI,gBAAgB;4BAClB,MAAM,iBAAiB,KAAK,SAAS,CAAC;gCACpC,MAAM;gCACN;gCACA;gCACA,QAAQ;gCACR;gCACA,eAAe;4BACjB;4BAEA,IAAI,CAAC,QAAU,CAAC;oCACd,UAAU,IAAI,IAAI,MAAM,QAAQ,EAAE,GAAG,CAAC,aAAa;wCACjD,GAAG,cAAc;wCACjB,SAAS;wCACT,aAAa;oCACf;gCACF,CAAC;wBACH;wBAEA,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,CAAC,4CAA4C,EAAE,aAAa,wBAAwB,UAAU,CAAC;wBACrG;oBACF;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI,MAAM,IAAI,KAAK,cAAc;oBAC/B,sBAAsB;oBACtB,IAAI,CAAC,QAAU,CAAC;4BACd,eAAe;gCACb,GAAG,MAAM,aAAa;gCACtB,YAAY;gCACZ,WAAW;4BACb;wBACF,CAAC;oBAED,MAAM,iBAAiB,MAAM,QAAQ,CAAC,GAAG,CAAC;oBAC1C,IAAI,gBAAgB;wBAClB,MAAM,iBAAiB,KAAK,SAAS,CAAC;4BACpC,MAAM;4BACN;4BACA;4BACA,QAAQ;wBACV;wBAEA,IAAI,CAAC,QAAU,CAAC;gCACd,UAAU,IAAI,IAAI,MAAM,QAAQ,EAAE,GAAG,CAAC,aAAa;oCACjD,GAAG,cAAc;oCACjB,SAAS;oCACT,aAAa;gCACf;4BACF,CAAC;oBACH;oBAEA,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE;gBACR,OAAO;oBACL,MAAM,WAAW,MAAM,OAAO,IAAI;oBAClC,IAAI,CAAC,QAAU,CAAC;4BACd,eAAe;gCACb,GAAG,MAAM,aAAa;gCACtB,YAAY;gCACZ,WAAW;4BACb;wBACF,CAAC;oBAED,MAAM,iBAAiB,MAAM,QAAQ,CAAC,GAAG,CAAC;oBAC1C,IAAI,gBAAgB;wBAClB,MAAM,iBAAiB,KAAK,SAAS,CAAC;4BACpC,MAAM;4BACN;4BACA;4BACA,QAAQ;4BACR,OAAO;wBACT;wBAEA,IAAI,CAAC,QAAU,CAAC;gCACd,UAAU,IAAI,IAAI,MAAM,QAAQ,EAAE,GAAG,CAAC,aAAa;oCACjD,GAAG,cAAc;oCACjB,SAAS;oCACT,aAAa;gCACf;4BACF,CAAC;oBACH;oBAEA,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE,CAAC,+BAA+B,EAAE,UAAU;gBACpD;YACF,SAAU;gBACR,4BAA4B;gBAC5B,OAAO,AAAC,MAAc,kBAAkB;YAC1C;QACF;QAEA;YACE,MAAM,kBAAkB,AAAC,MAAc,kBAAkB;YACzD,IAAI,iBAAiB;gBACnB,gBAAgB,KAAK;YACvB;QACF;QAEA;YACE,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,WAAW;oBACb;gBACF,CAAC;QACH;IACF,CAAC;AAEM,eAAe,YACpB,OAAgB,EAChB,EACE,iBAAiB,EAGlB,GAAG,CAAC,CAAC,EACN,UAAyC,CAAC,CAAC;IAE3C,IAAI,WAAW,MAAM;QACnB,cAAc;YACZ,IAAI,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;YACT,UAAU;YACV,MAAM;YACN,SAAS;YACT,eAAe;gBAAC;aAAQ;QAC1B;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,wBAAqB,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EACtB,WAAW,YACX;QACE,WAAW;QACX,oBAAoB;QACpB,oBAAoB,SAAS,gBAAgB;QAC7C,iCACE,SAAS,6BAA6B,IAAI;QAC5C,qBAAqB,SAAS,iBAAiB;QAC/C,cAAc,SAAS,UAAU;QACjC,oBAAoB,SAAS,gBAAgB;QAC7C,cAAc,SAAS,WAAW;IACpC,GACA;IAGF,cAAc;IACd,IAAI;IACJ,IAAI;QACF,WAAW,MAAM,SAAS,OAAQ;YAChC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;YACvB,YAAY,KAAK,EAAE;YACnB,IAAI;YACJ,IAAI,SAAS,oBAAoB;gBAC/B,UAAU,wBAAwB,KAAK,YAAY;YACrD,OAAO,IAAI,CAAC,cAAc,YAAY;gBACpC,UAAU;oBACR,IAAI;oBACJ,UAAU,KAAK,SAAS;oBACxB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,SAAS;oBACT,eAAe,EAAE;oBACjB,aAAa;oBACb;gBACF;gBACA,cAAc;YAChB;YACA,YAAY,WAAW;YACvB,IAAI,SAAS;gBACX,UAAU,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE,SAAS;gBAChC,cAAc;YAChB;QACF;IACF,EAAE,OAAM;QACN,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE;QACN,yBAAyB;QACzB,kEAAkE;QAClE,IAAI,aAAa,MAAM;YACrB,MAAM,UAAU,WAAW;YAC3B,IAAI,SAAS,aAAa;gBACxB,QAAQ,WAAW,GAAG;gBACtB,SAAS,QAAQ,GAAG,aAAa,CAAC;YACpC;QACF;QACA,SAAS,QAAQ,GAAG,kBAAkB,CAAC;IACzC,SAAU;QACR,cAAc;IAChB;AACF;AAEA,SAAS,cAAc,KAAc;IACnC,SAAS,QAAQ,CAAC;QAAE,YAAY;IAAM;AACxC;AAEA,SAAS,cAAc,EAAU;IAC/B,OAAO,SAAS,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;AACjD;AAEA,SAAS,WAAW,EAAU;IAC5B,OAAO,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC;AAC1C;AAEA,SAAS,wBAAwB,UAAkB;IACjD,OAAO,MAAM,IAAI,CAAC,SAAS,QAAQ,GAAG,QAAQ,CAAC,MAAM,IAClD,OAAO,GACP,IAAI,CAAC,CAAC;QACL,IAAI,QAAQ,SAAS,EAAE;YACrB,OAAO,QAAQ,SAAS,CAAC,IAAI,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK;QAC9D;QACA,OAAO;IACT;AACJ;AAEA,SAAS,cAAc,OAAgB;IACrC,IACE,QAAQ,KAAK,KAAK,WAClB,QAAQ,KAAK,KAAK,cAClB,QAAQ,KAAK,KAAK,cAClB;QACA,IAAI,CAAC,wBAAwB;YAC3B,MAAM,KAAK,QAAQ,EAAE;YACrB,eAAe;YACf,aAAa;QACf;QACA,uBAAuB;IACzB;IACA,SAAS,QAAQ,GAAG,aAAa,CAAC;AACpC;AAEA,SAAS,cAAc,OAAgB;IACrC,IACE,0BACA,QAAQ,KAAK,KAAK,cAClB,CAAC,QAAQ,WAAW,EACpB;QACA,SAAS,QAAQ,GAAG,kBAAkB,CAAC;IACzC;IACA,SAAS,QAAQ,GAAG,aAAa,CAAC;AACpC;AAEA,SAAS;IACP,OAAO,SAAS,QAAQ,GAAG,iBAAiB;AAC9C;AAEA,SAAS,eAAe,UAAkB;IACxC,IAAI;IACJ,MAAM,qBAAqB;WAAI,SAAS,QAAQ,GAAG,UAAU;KAAC,CAAC,OAAO;IACtE,KAAK,MAAM,aAAa,mBAAoB;QAC1C,MAAM,UAAU,WAAW;QAC3B,IAAI,SAAS,UAAU,WAAW;YAChC,cAAc;YACd;QACF;IACF;IACA,MAAM,aAAa;QAAC;KAAW;IAC/B,WAAW,OAAO,CAAC,YAAa,EAAE;IAClC,SAAS,QAAQ,CAAC;QAChB,mBAAmB;QACnB,aAAa;eAAI,SAAS,QAAQ,GAAG,WAAW;YAAE;SAAW;QAC7D,iBAAiB,IAAI,IAAI,SAAS,QAAQ,GAAG,eAAe,EAAE,GAAG,CAC/D,YACA,YAAa,EAAE;QAEjB,qBAAqB,IAAI,IAAI,SAAS,QAAQ,GAAG,mBAAmB,EAAE,GAAG,CACvE,YACA;IAEJ;AACF;AAEA,SAAS,uBAAuB,OAAgB;IAC9C,MAAM,aAAa;IACnB,IAAI,YAAY;QACd,MAAM,sBAAsB,SAAS,QAAQ,GAAG,mBAAmB;QACnE,MAAM,UAAU,oBAAoB,GAAG,CAAC;QACxC,IAAI,CAAC,QAAQ,QAAQ,CAAC,QAAQ,EAAE,GAAG;YACjC,SAAS,QAAQ,CAAC;gBAChB,qBAAqB,IAAI,IAAI,qBAAqB,GAAG,CAAC,YAAY;uBAC7D;oBACH,QAAQ,EAAE;iBACX;YACH;QACF;QACA,IAAI,QAAQ,KAAK,KAAK,YAAY;YAChC,SAAS,QAAQ,CAAC;gBAChB,mBAAmB,IAAI,IAAI,SAAS,QAAQ,GAAG,iBAAiB,EAAE,GAAG,CACnE,YACA,QAAQ,EAAE;YAEd;QACF;IACF;AACF;AAEO,SAAS,aAAa,UAAyB;IACpD,SAAS,QAAQ,GAAG,YAAY,CAAC;AACnC;AAEO,SAAS;IACd,SAAS,QAAQ,GAAG,aAAa;AACnC;AAEO,eAAe,gBAAgB,UAAkB;IACtD,MAAM,gBAAgB,SAAS,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC;IAC9D,MAAM,kBAAkB,SAAS,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC;IAClE,IAAI,iBAAiB,iBAAiB;QACpC,MAAM,cAAc,WAAW;QAC/B,MAAM,QAAQ,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE,YAAY,OAAO,EAAE;YAAE,OAAO;QAAW,GAAG,KAAK;QACzE,MAAM,gBAAgB,WAAW;QACjC,IAAI,eAAe,SAAS;YAC1B,cAAc;gBACZ,IAAI,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;gBACT,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,eAAe,EAAE;YACnB;YACA,MAAM,mBAAmB,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD;YAC9B,MAAM,gBAAgB;gBAAE;gBAAO;YAAW;YAC1C,MAAM,iBAA0B;gBAC9B,IAAI;gBACJ,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,SAAS,KAAK,SAAS,CAAC;gBACxB,eAAe,EAAE;gBACjB,aAAa;YACf;YACA,cAAc;YACd,wBAAwB;YACxB,IAAI;YACJ,IAAI;gBACF,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,cAAc,OAAO;YACxD,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC;gBACd,SAAS,QAAQ,CAAC,CAAC,QAAU,CAAC;wBAC5B,UAAU,IAAI,IAAI,SAAS,QAAQ,GAAG,QAAQ,EAAE,GAAG,CACjD,kBACA;4BACE,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,iBAAiB;4BACvC,SAAS,KAAK,SAAS,CAAC;gCACtB,GAAG,aAAa;gCAChB,OAAO,aAAa,QAAQ,EAAE,OAAO,GAAG;4BAC1C;4BACA,aAAa;wBACf;oBAEJ,CAAC;gBACD,CAAA,GAAA,2QAAA,CAAA,QAAK,AAAD,EAAE;gBACN;YACF;YACA,SAAS,QAAQ,CAAC,CAAC,QAAU,CAAC;oBAC5B,UAAU,IAAI,IAAI,SAAS,QAAQ,GAAG,QAAQ,EAAE,GAAG,CAAC,kBAAkB;wBACpE,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,iBAAiB;wBACvC,SAAS,KAAK,SAAS,CAAC;4BAAE,GAAG,aAAa;4BAAE;wBAAS;wBACrD,aAAa;oBACf;gBACF,CAAC;QACH;IACF;AACF;AAEO,SAAS,mBAAmB,UAAkB;;IACnD,OAAO,SACL,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;kDAAE,CAAC;YACV,MAAM,YAAY,MAAM,eAAe,CAAC,GAAG,CAAC;YAC5C,OAAO,YAAY,MAAM,QAAQ,CAAC,GAAG,CAAC,aAAa;QACrD;;AAEJ;GAPgB;;QACP;;;AAQF,SAAS,WAAW,SAAoC;;IAC7D,OAAO,SACL,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;0CAAE,CAAC,QACV,YAAY,MAAM,QAAQ,CAAC,GAAG,CAAC,aAAa;;AAGlD;IANgB;;QACP;;;AAOF,SAAS;;IACd,OAAO,SAAS,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;6CAAE,CAAC,QAAU,MAAM,UAAU;;AACxD;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,SACL,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;uDAAE,CAAC;YACV,IAAI,MAAM,UAAU,CAAC,MAAM,IAAI,GAAG;gBAChC,MAAM,cAAc,MAAM,QAAQ,CAAC,GAAG,CACpC,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE;gBAE/C,OAAO,aAAa,iBAAiB,cAAc,cAAc;YACnE;YACA,OAAO;QACT;;AAEJ;IAZgB;;QACP;;;AAaF,SAAS;;IACd,MAAM,8BAA8B,SAClC,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;0EAAE,CAAC;YACV,IAAI,MAAM,UAAU,CAAC,MAAM,IAAI,GAAG;gBAChC,MAAM,cAAc,MAAM,QAAQ,CAAC,GAAG,CACpC,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE;gBAE/C,IAAI,eAAe,YAAY,YAAY,KAAK,aAAa;oBAC3D,OAAO,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE;gBACtD;YACF;YACA,OAAO;QACT;;IAEF,OAAO;AACT;IAfgB;;QACsB;;;AAgB/B,SAAS;;IACd,OAAO,SACL,CAAA,GAAA,kQAAA,CAAA,aAAU,AAAD;4CAAE,CAAC;YACV,OAAO,MAAM,UAAU,EACnB;oDAAI,CAAC,KAAO,WAAW,KAAK;mDAC7B;oDAAO,CAAC,YAAc,aAAa;mDACnC;QACL;;AAEJ;IATgB;;QACP;;;AAWF,SAAS;;IACd,OAAO;0CAAS,CAAC,QAAU,MAAM,aAAa;;AAChD;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO;4CAAS,CAAC,QAAU,CAAC;gBAC1B,YAAY,MAAM,uBAAuB;gBACzC,WAAW,MAAM,sBAAsB;gBACvC,YAAY,MAAM,uBAAuB;YAC3C,CAAC;;AACH;IANgB;;QACP", "debugId": null}}, {"offset": {"line": 2681, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/store/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./store\";\r\nexport * from \"./settings-store\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B;AACA", "debugId": null}}, {"offset": {"line": 2707, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/rehype/rehype-split-words-into-spans.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport type { Element, Root, ElementContent } from \"hast\";\r\nimport { visit } from \"unist-util-visit\";\r\nimport type { BuildVisitor } from \"unist-util-visit\";\r\n\r\nexport function rehypeSplitWordsIntoSpans() {\r\n  return (tree: Root) => {\r\n    visit(tree, \"element\", ((node: Element) => {\r\n      if (\r\n        [\"p\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"li\", \"strong\"].includes(\r\n          node.tagName,\r\n        ) &&\r\n        node.children\r\n      ) {\r\n        const newChildren: Array<ElementContent> = [];\r\n        node.children.forEach((child) => {\r\n          if (child.type === \"text\") {\r\n            const segmenter = new Intl.Segmenter(\"zh\", { granularity: \"word\" });\r\n            const segments = segmenter.segment(child.value);\r\n            const words = Array.from(segments)\r\n              .map((segment) => segment.segment)\r\n              .filter(Boolean);\r\n            words.forEach((word: string) => {\r\n              newChildren.push({\r\n                type: \"element\",\r\n                tagName: \"span\",\r\n                properties: {\r\n                  className: \"animate-fade-in\",\r\n                },\r\n                children: [{ type: \"text\", value: word }],\r\n              });\r\n            });\r\n          } else {\r\n            newChildren.push(child);\r\n          }\r\n        });\r\n        node.children = newChildren;\r\n      }\r\n    }) as BuildVisitor<Root, \"element\">);\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAG/B;;AAGO,SAAS;IACd,OAAO,CAAC;QACN,CAAA,GAAA,4OAAA,CAAA,QAAK,AAAD,EAAE,MAAM,WAAY,CAAC;YACvB,IACE;gBAAC;gBAAK;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAS,CAAC,QAAQ,CAChE,KAAK,OAAO,KAEd,KAAK,QAAQ,EACb;gBACA,MAAM,cAAqC,EAAE;gBAC7C,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;oBACrB,IAAI,MAAM,IAAI,KAAK,QAAQ;wBACzB,MAAM,YAAY,IAAI,KAAK,SAAS,CAAC,MAAM;4BAAE,aAAa;wBAAO;wBACjE,MAAM,WAAW,UAAU,OAAO,CAAC,MAAM,KAAK;wBAC9C,MAAM,QAAQ,MAAM,IAAI,CAAC,UACtB,GAAG,CAAC,CAAC,UAAY,QAAQ,OAAO,EAChC,MAAM,CAAC;wBACV,MAAM,OAAO,CAAC,CAAC;4BACb,YAAY,IAAI,CAAC;gCACf,MAAM;gCACN,SAAS;gCACT,YAAY;oCACV,WAAW;gCACb;gCACA,UAAU;oCAAC;wCAAE,MAAM;wCAAQ,OAAO;oCAAK;iCAAE;4BAC3C;wBACF;oBACF,OAAO;wBACL,YAAY,IAAI,CAAC;oBACnB;gBACF;gBACA,KAAK,QAAQ,GAAG;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2769, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/rehype/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./rehype-split-words-into-spans\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B", "debugId": null}}, {"offset": {"line": 2792, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/utils/markdown.ts"], "sourcesContent": ["export function autoFixMarkdown(markdown: string): string {\r\n  return autoCloseTrailingLink(markdown);\r\n}\r\n\r\nfunction autoCloseTrailingLink(markdown: string): string {\r\n  // Fix unclosed Markdown links or images\r\n  let fixedMarkdown: string = markdown;\r\n\r\n  // Fix unclosed image syntax ![...](...)\r\n  fixedMarkdown = fixedMarkdown.replace(\r\n    /!\\[([^\\]]*)\\]\\(([^)]*)$/g,\r\n    (match: string, altText: string, url: string): string => {\r\n      return `![${altText}](${url})`;\r\n    },\r\n  );\r\n\r\n  // Fix unclosed link syntax [...](...)\r\n  fixedMarkdown = fixedMarkdown.replace(\r\n    /\\[([^\\]]*)\\]\\(([^)]*)$/g,\r\n    (match: string, linkText: string, url: string): string => {\r\n      return `[${linkText}](${url})`;\r\n    },\r\n  );\r\n\r\n  // Fix unclosed image syntax ![...]\r\n  fixedMarkdown = fixedMarkdown.replace(\r\n    /!\\[([^\\]]*)$/g,\r\n    (match: string, altText: string): string => {\r\n      return `![${altText}]`;\r\n    },\r\n  );\r\n\r\n  // Fix unclosed link syntax [...]\r\n  fixedMarkdown = fixedMarkdown.replace(\r\n    /\\[([^\\]]*)$/g,\r\n    (match: string, linkText: string): string => {\r\n      return `[${linkText}]`;\r\n    },\r\n  );\r\n\r\n  // Fix unclosed images or links missing \")\"\r\n  fixedMarkdown = fixedMarkdown.replace(\r\n    /!\\[([^\\]]*)\\]\\(([^)]*)$/g,\r\n    (match: string, altText: string, url: string): string => {\r\n      return `![${altText}](${url})`;\r\n    },\r\n  );\r\n\r\n  fixedMarkdown = fixedMarkdown.replace(\r\n    /\\[([^\\]]*)\\]\\(([^)]*)$/g,\r\n    (match: string, linkText: string, url: string): string => {\r\n      return `[${linkText}](${url})`;\r\n    },\r\n  );\r\n\r\n  return fixedMarkdown;\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,SAAS,gBAAgB,QAAgB;IAC9C,OAAO,sBAAsB;AAC/B;AAEA,SAAS,sBAAsB,QAAgB;IAC7C,wCAAwC;IACxC,IAAI,gBAAwB;IAE5B,wCAAwC;IACxC,gBAAgB,cAAc,OAAO,CACnC,4BACA,CAAC,OAAe,SAAiB;QAC/B,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;IAChC;IAGF,sCAAsC;IACtC,gBAAgB,cAAc,OAAO,CACnC,2BACA,CAAC,OAAe,UAAkB;QAChC,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;IAChC;IAGF,mCAAmC;IACnC,gBAAgB,cAAc,OAAO,CACnC,iBACA,CAAC,OAAe;QACd,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;IACxB;IAGF,iCAAiC;IACjC,gBAAgB,cAAc,OAAO,CACnC,gBACA,CAAC,OAAe;QACd,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IACxB;IAGF,2CAA2C;IAC3C,gBAAgB,cAAc,OAAO,CACnC,4BACA,CAAC,OAAe,SAAiB;QAC/B,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;IAChC;IAGF,gBAAgB,cAAc,OAAO,CACnC,2BACA,CAAC,OAAe,UAAkB;QAChC,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;IAChC;IAGF,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2835, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/image.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { memo, useCallback, useEffect, useState } from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { Tooltip } from \"./tooltip\";\r\n\r\nfunction Image({\r\n  className,\r\n  imageClassName,\r\n  imageTransition,\r\n  src,\r\n  alt,\r\n  fallback = null,\r\n}: {\r\n  className?: string;\r\n  imageClassName?: string;\r\n  imageTransition?: boolean;\r\n  src: string;\r\n  alt: string;\r\n  fallback?: React.ReactNode;\r\n}) {\r\n  const [, setIsLoading] = useState(true);\r\n  const [isError, setIsError] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsError(false);\r\n    setIsLoading(true);\r\n  }, [src]);\r\n\r\n  const handleLoad = useCallback(() => {\r\n    setIsError(false);\r\n    setIsLoading(false);\r\n  }, []);\r\n  const handleError = useCallback(\r\n    (e: React.SyntheticEvent<HTMLImageElement>) => {\r\n      e.currentTarget.style.display = \"none\";\r\n      console.warn(`Markdown: Image \"${e.currentTarget.src}\" failed to load`);\r\n      setIsError(true);\r\n    },\r\n    [],\r\n  );\r\n  return (\r\n    <span className={cn(\"block w-fit overflow-hidden\", className)}>\r\n      {isError || !src ? (\r\n        fallback\r\n      ) : (\r\n        <Tooltip title={alt ?? \"No caption\"}>\r\n          <img\r\n            className={cn(\r\n              \"size-full object-cover\",\r\n              imageTransition && \"transition-all duration-200 ease-out\",\r\n              imageClassName,\r\n            )}\r\n            src={src}\r\n            alt={alt}\r\n            onLoad={handleLoad}\r\n            onError={handleError}\r\n          />\r\n        </Tooltip>\r\n      )}\r\n    </span>\r\n  );\r\n}\r\n\r\nexport default memo(Image);\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AAEA;;;;;;AAEA,SAAS,MAAM,EACb,SAAS,EACT,cAAc,EACd,eAAe,EACf,GAAG,EACH,GAAG,EACH,WAAW,IAAI,EAQhB;;IACC,MAAM,GAAG,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAClC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;2BAAE;YACR,WAAW;YACX,aAAa;QACf;0BAAG;QAAC;KAAI;IAER,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;yCAAE;YAC7B,WAAW;YACX,aAAa;QACf;wCAAG,EAAE;IACL,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;0CAC5B,CAAC;YACC,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;YAChC,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,EAAE,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACtE,WAAW;QACb;yCACA,EAAE;IAEJ,qBACE,sSAAC;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAChD,WAAW,CAAC,MACX,yBAEA,sSAAC,gJAAA,CAAA,UAAO;YAAC,OAAO,OAAO;sBACrB,cAAA,sSAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0BACA,mBAAmB,wCACnB;gBAEF,KAAK;gBACL,KAAK;gBACL,QAAQ;gBACR,SAAS;;;;;;;;;;;;;;;;AAMrB;GAxDS;KAAA;2DA0DM,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 2915, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/link.tsx"], "sourcesContent": ["import { useMemo } from \"react\";\r\nimport { useStore, useToolCalls } from \"~/core/store\";\r\nimport { Tooltip } from \"./tooltip\";\r\nimport { WarningFilled } from \"@ant-design/icons\";\r\n\r\nexport const Link = ({\r\n  href,\r\n  children,\r\n  checkLinkCredibility = false,\r\n}: {\r\n  href: string | undefined;\r\n  children: React.ReactNode;\r\n  checkLinkCredibility: boolean;\r\n}) => {\r\n  const toolCalls = useToolCalls();\r\n  const responding = useStore((state) => state.responding);\r\n\r\n  const credibleLinks = useMemo(() => {\r\n    const links = new Set<string>();\r\n    if (!checkLinkCredibility) return links;\r\n\r\n    (toolCalls || []).forEach((call) => {\r\n      if (call && call.name === \"web_search\" && call.result) {\r\n        const result = JSON.parse(call.result) as Array<{ url: string }>;\r\n        result.forEach((r) => {\r\n          links.add(r.url);\r\n        });\r\n      }\r\n    });\r\n    return links;\r\n  }, [toolCalls]);\r\n\r\n  const isCredible = useMemo(() => {\r\n    return checkLinkCredibility && href && !responding\r\n      ? credibleLinks.has(href)\r\n      : true;\r\n  }, [credibleLinks, href, responding, checkLinkCredibility]);\r\n\r\n  return (\r\n    <span className=\"flex items-center gap-1.5\">\r\n      <a href={href} target=\"_blank\" rel=\"noopener noreferrer\">\r\n        {children}\r\n      </a>\r\n      {!isCredible && (\r\n        <Tooltip\r\n          title=\"This link might be a hallucination from AI model and may not be reliable.\"\r\n          delayDuration={300}\r\n        >\r\n          <WarningFilled className=\"text-sx transition-colors hover:!text-yellow-500\" />\r\n        </Tooltip>\r\n      )}\r\n    </span>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;;;;;;;AAEO,MAAM,OAAO,CAAC,EACnB,IAAI,EACJ,QAAQ,EACR,uBAAuB,KAAK,EAK7B;;IACC,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAC7B,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;qCAAE,CAAC,QAAU,MAAM,UAAU;;IAEvD,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;uCAAE;YAC5B,MAAM,QAAQ,IAAI;YAClB,IAAI,CAAC,sBAAsB,OAAO;YAElC,CAAC,aAAa,EAAE,EAAE,OAAO;+CAAC,CAAC;oBACzB,IAAI,QAAQ,KAAK,IAAI,KAAK,gBAAgB,KAAK,MAAM,EAAE;wBACrD,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM;wBACrC,OAAO,OAAO;2DAAC,CAAC;gCACd,MAAM,GAAG,CAAC,EAAE,GAAG;4BACjB;;oBACF;gBACF;;YACA,OAAO;QACT;sCAAG;QAAC;KAAU;IAEd,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oCAAE;YACzB,OAAO,wBAAwB,QAAQ,CAAC,aACpC,cAAc,GAAG,CAAC,QAClB;QACN;mCAAG;QAAC;QAAe;QAAM;QAAY;KAAqB;IAE1D,qBACE,sSAAC;QAAK,WAAU;;0BACd,sSAAC;gBAAE,MAAM;gBAAM,QAAO;gBAAS,KAAI;0BAChC;;;;;;YAEF,CAAC,4BACA,sSAAC,gJAAA,CAAA,UAAO;gBACN,OAAM;gBACN,eAAe;0BAEf,cAAA,sSAAC,iUAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKnC;GAhDa;;QASO,gIAAA,CAAA,eAAY;QACX,gIAAA,CAAA,WAAQ;;;KAVhB", "debugId": null}}, {"offset": {"line": 3020, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/markdown.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Check, Copy } from \"lucide-react\";\r\nimport { useMemo, useState } from \"react\";\r\nimport ReactMarkdown, {\r\n  type Options as ReactMarkdownOptions,\r\n} from \"react-markdown\";\r\nimport rehypeKatex from \"rehype-katex\";\r\nimport remarkGfm from \"remark-gfm\";\r\nimport remarkMath from \"remark-math\";\r\nimport \"katex/dist/katex.min.css\";\r\n\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { rehypeSplitWordsIntoSpans } from \"~/core/rehype\";\r\nimport { autoFixMarkdown } from \"~/core/utils/markdown\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport Image from \"./image\";\r\nimport { Tooltip } from \"./tooltip\";\r\nimport { Link } from \"./link\";\r\n\r\nexport function Markdown({\r\n  className,\r\n  children,\r\n  style,\r\n  enableCopy,\r\n  animated = false,\r\n  checkLinkCredibility = false,\r\n  ...props\r\n}: ReactMarkdownOptions & {\r\n  className?: string;\r\n  enableCopy?: boolean;\r\n  style?: React.CSSProperties;\r\n  animated?: boolean;\r\n  checkLinkCredibility?: boolean;\r\n}) {\r\n  const components: ReactMarkdownOptions[\"components\"] = useMemo(() => {\r\n    return {\r\n      a: ({ href, children }) => (\r\n        <Link href={href} checkLinkCredibility={checkLinkCredibility}>\r\n          {children}\r\n        </Link>\r\n      ),\r\n      img: ({ src, alt }) => (\r\n        <a href={src as string} target=\"_blank\" rel=\"noopener noreferrer\">\r\n          <Image className=\"rounded\" src={src as string} alt={alt ?? \"\"} />\r\n        </a>\r\n      ),\r\n    };\r\n  }, [checkLinkCredibility]);\r\n\r\n  const rehypePlugins = useMemo(() => {\r\n    if (animated) {\r\n      return [rehypeKatex, rehypeSplitWordsIntoSpans];\r\n    }\r\n    return [rehypeKatex];\r\n  }, [animated]);\r\n  return (\r\n    <div className={cn(className, \"prose dark:prose-invert\")} style={style}>\r\n      <ReactMarkdown\r\n        remarkPlugins={[remarkGfm, remarkMath]}\r\n        rehypePlugins={rehypePlugins}\r\n        components={components}\r\n        {...props}\r\n      >\r\n        {autoFixMarkdown(\r\n          dropMarkdownQuote(processKatexInMarkdown(children ?? \"\")) ?? \"\",\r\n        )}\r\n      </ReactMarkdown>\r\n      {enableCopy && typeof children === \"string\" && (\r\n        <div className=\"flex\">\r\n          <CopyButton content={children} />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction CopyButton({ content }: { content: string }) {\r\n  const [copied, setCopied] = useState(false);\r\n  return (\r\n    <Tooltip title=\"Copy\">\r\n      <Button\r\n        variant=\"outline\"\r\n        size=\"sm\"\r\n        className=\"rounded-full\"\r\n        onClick={async () => {\r\n          try {\r\n            await navigator.clipboard.writeText(content);\r\n            setCopied(true);\r\n            setTimeout(() => {\r\n              setCopied(false);\r\n            }, 1000);\r\n          } catch (error) {\r\n            console.error(error);\r\n          }\r\n        }}\r\n      >\r\n        {copied ? (\r\n          <Check className=\"h-4 w-4\" />\r\n        ) : (\r\n          <Copy className=\"h-4 w-4\" />\r\n        )}{\" \"}\r\n      </Button>\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction processKatexInMarkdown(markdown?: string | null) {\r\n  if (!markdown) return markdown;\r\n\r\n  const markdownWithKatexSyntax = markdown\r\n    .replace(/\\\\\\\\\\[/g, \"$$$$\") // Replace '\\\\[' with '$$'\r\n    .replace(/\\\\\\\\\\]/g, \"$$$$\") // Replace '\\\\]' with '$$'\r\n    .replace(/\\\\\\\\\\(/g, \"$$$$\") // Replace '\\\\(' with '$$'\r\n    .replace(/\\\\\\\\\\)/g, \"$$$$\") // Replace '\\\\)' with '$$'\r\n    .replace(/\\\\\\[/g, \"$$$$\") // Replace '\\[' with '$$'\r\n    .replace(/\\\\\\]/g, \"$$$$\") // Replace '\\]' with '$$'\r\n    .replace(/\\\\\\(/g, \"$$$$\") // Replace '\\(' with '$$'\r\n    .replace(/\\\\\\)/g, \"$$$$\"); // Replace '\\)' with '$$';\r\n  return markdownWithKatexSyntax;\r\n}\r\n\r\nfunction dropMarkdownQuote(markdown?: string | null) {\r\n  if (!markdown) return markdown;\r\n  return markdown\r\n    .replace(/^```markdown\\n/gm, \"\")\r\n    .replace(/^```text\\n/gm, \"\")\r\n    .replace(/^```\\n/gm, \"\")\r\n    .replace(/\\n```$/gm, \"\");\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;AAEO,SAAS,SAAS,EACvB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,uBAAuB,KAAK,EAC5B,GAAG,OAOJ;;IACC,MAAM,aAAiD,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wCAAE;YAC7D,OAAO;gBACL,CAAC;oDAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,iBACpB,sSAAC,6IAAA,CAAA,OAAI;4BAAC,MAAM;4BAAM,sBAAsB;sCACrC;;;;;;;gBAGL,GAAG;oDAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,iBAChB,sSAAC;4BAAE,MAAM;4BAAe,QAAO;4BAAS,KAAI;sCAC1C,cAAA,sSAAC,8IAAA,CAAA,UAAK;gCAAC,WAAU;gCAAU,KAAK;gCAAe,KAAK,OAAO;;;;;;;;;;;;YAGjE;QACF;uCAAG;QAAC;KAAqB;IAEzB,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;2CAAE;YAC5B,IAAI,UAAU;gBACZ,OAAO;oBAAC,8MAAA,CAAA,UAAW;oBAAE,qKAAA,CAAA,4BAAyB;iBAAC;YACjD;YACA,OAAO;gBAAC,8MAAA,CAAA,UAAW;aAAC;QACtB;0CAAG;QAAC;KAAS;IACb,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QAA4B,OAAO;;0BAC/D,sSAAC,mTAAA,CAAA,UAAa;gBACZ,eAAe;oBAAC,0MAAA,CAAA,UAAS;oBAAE,4MAAA,CAAA,UAAU;iBAAC;gBACtC,eAAe;gBACf,YAAY;gBACX,GAAG,KAAK;0BAER,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EACb,kBAAkB,uBAAuB,YAAY,QAAQ;;;;;;YAGhE,cAAc,OAAO,aAAa,0BACjC,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC;oBAAW,SAAS;;;;;;;;;;;;;;;;;AAK/B;GAvDgB;KAAA;AAyDhB,SAAS,WAAW,EAAE,OAAO,EAAuB;;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,qBACE,sSAAC,gJAAA,CAAA,UAAO;QAAC,OAAM;kBACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAU;YACV,SAAS;gBACP,IAAI;oBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;oBACpC,UAAU;oBACV,WAAW;wBACT,UAAU;oBACZ,GAAG;gBACL,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC;gBAChB;YACF;;gBAEC,uBACC,sSAAC,2RAAA,CAAA,QAAK;oBAAC,WAAU;;;;;yCAEjB,sSAAC,yRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBACf;;;;;;;;;;;;AAIX;IA5BS;MAAA;AA8BT,SAAS,uBAAuB,QAAwB;IACtD,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,0BAA0B,SAC7B,OAAO,CAAC,WAAW,QAAQ,0BAA0B;KACrD,OAAO,CAAC,WAAW,QAAQ,0BAA0B;KACrD,OAAO,CAAC,WAAW,QAAQ,0BAA0B;KACrD,OAAO,CAAC,WAAW,QAAQ,0BAA0B;KACrD,OAAO,CAAC,SAAS,QAAQ,yBAAyB;KAClD,OAAO,CAAC,SAAS,QAAQ,yBAAyB;KAClD,OAAO,CAAC,SAAS,QAAQ,yBAAyB;KAClD,OAAO,CAAC,SAAS,SAAS,0BAA0B;IACvD,OAAO;AACT;AAEA,SAAS,kBAAkB,QAAwB;IACjD,IAAI,CAAC,UAAU,OAAO;IACtB,OAAO,SACJ,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,gBAAgB,IACxB,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,YAAY;AACzB", "debugId": null}}, {"offset": {"line": 3231, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/settings/tabs/about.md.js"], "sourcesContent": ["export default \"# 🦌 [About DeerFlow](https://github.com/bytedance/deer-flow)\\r\\n\\r\\n> **From Open Source, Back to Open Source**\\r\\n\\r\\n**DeerFlow** (**D**eep **E**xploration and **E**fficient **R**esearch **Flow**) is a community-driven AI automation framework inspired by the remarkable contributions of the open source community. Our mission is to seamlessly integrate language models with specialized tools for tasks such as web search, crawling, and Python code execution—all while giving back to the community that made this innovation possible.\\r\\n\\r\\n---\\r\\n\\r\\n## 🌟 GitHub Repository\\r\\n\\r\\nExplore DeerFlow on GitHub: [github.com/bytedance/deer-flow](https://github.com/bytedance/deer-flow)\\r\\n\\r\\n---\\r\\n\\r\\n## 📜 License\\r\\n\\r\\nDeerFlow is proudly open source and distributed under the **MIT License**.\\r\\n\\r\\n---\\r\\n\\r\\n## 🙌 Acknowledgments\\r\\n\\r\\nWe extend our heartfelt gratitude to the open source projects and contributors who have made DeerFlow a reality. We truly stand on the shoulders of giants.\\r\\n\\r\\n### Core Frameworks\\r\\n- **[LangChain](https://github.com/langchain-ai/langchain)**: A phenomenal framework that powers our LLM interactions and chains.\\r\\n- **[LangGraph](https://github.com/langchain-ai/langgraph)**: Enabling sophisticated multi-agent orchestration.\\r\\n- **[Next.js](https://nextjs.org/)**: A cutting-edge framework for building web applications.\\r\\n\\r\\n### UI Libraries\\r\\n- **[Shadcn](https://ui.shadcn.com/)**: Minimalistic components that power our UI.\\r\\n- **[Zustand](https://zustand.docs.pmnd.rs/)**: A stunning state management library.\\r\\n- **[Framer Motion](https://www.framer.com/motion/)**: An amazing animation library.\\r\\n- **[React Markdown](https://www.npmjs.com/package/react-markdown)**: Exceptional markdown rendering with customizability.\\r\\n- **[SToneX](https://github.com/stonexer)**: For his invaluable contribution to token-by-token visual effects.\\r\\n\\r\\nThese outstanding projects form the backbone of DeerFlow and exemplify the transformative power of open source collaboration.\\r\\n\\r\\n### Special Thanks\\r\\nFinally, we want to express our heartfelt gratitude to the core authors of `DeerFlow`:\\r\\n\\r\\n- **[Daniel Walnut](https://github.com/hetaoBackend/)**\\r\\n- **[Henry Li](https://github.com/magiccube/)**\\r\\n\\r\\nWithout their vision, passion and dedication, `DeerFlow` would not be what it is today.\\r\\n\";"], "names": [], "mappings": ";;;uCAAe", "debugId": null}}, {"offset": {"line": 3244, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/settings/tabs/about-tab.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { BadgeInfo } from \"lucide-react\";\r\n\r\nimport { Markdown } from \"~/components/deer-flow/markdown\";\r\n\r\nimport about from \"./about.md\";\r\nimport type { Tab } from \"./types\";\r\n\r\nexport const AboutTab: Tab = () => {\r\n  return <Markdown>{about}</Markdown>;\r\n};\r\nAboutTab.icon = BadgeInfo;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AAEA;;;;;AAGO,MAAM,WAAgB;IAC3B,qBAAO,sSAAC,iJAAA,CAAA,WAAQ;kBAAE,gJAAA,CAAA,UAAK;;;;;;AACzB;KAFa;AAGb,SAAS,IAAI,GAAG,uSAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 3279, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,sSAAC,iRAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3313, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\nimport { Label } from \"~/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,0PAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,sQAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,sSAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,sSAAC,0PAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0PAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,0PAAA,CAAA,iBAAc;QACtB,0PAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,sQAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,sQAAA,CAAA,QAAW,AAAD;IAErB,qBACE,sSAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,sSAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,sSAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,sSAAC,uSAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,sSAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,sSAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 3515, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,sSAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3547, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Switch({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\r\n  return (\r\n    <SwitchPrimitive.Root\r\n      data-slot=\"switch\"\r\n      className={cn(\r\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SwitchPrimitive.Thumb\r\n        data-slot=\"switch-thumb\"\r\n        className={cn(\r\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\r\n        )}\r\n      />\r\n    </SwitchPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,sSAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,kRAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV;KArBS", "debugId": null}}, {"offset": {"line": 3589, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/settings/tabs/general-tab.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { Settings } from \"lucide-react\";\r\nimport { useEffect, useMemo } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\n\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"~/components/ui/form\";\r\nimport { Input } from \"~/components/ui/input\";\r\nimport { Label } from \"~/components/ui/label\";\r\nimport { Switch } from \"~/components/ui/switch\";\r\nimport type { SettingsState } from \"~/core/store\";\r\n\r\nimport type { Tab } from \"./types\";\r\n\r\nconst generalFormSchema = z.object({\r\n  autoAcceptedPlan: z.boolean(),\r\n  enableBackgroundInvestigation: z.boolean(),\r\n  maxPlanIterations: z.number().min(1, {\r\n    message: \"Max plan iterations must be at least 1.\",\r\n  }),\r\n  maxStepNum: z.number().min(1, {\r\n    message: \"Max step number must be at least 1.\",\r\n  }),\r\n  maxSearchResults: z.number().min(1, {\r\n    message: \"Max search results must be at least 1.\",\r\n  }),\r\n});\r\n\r\nexport const GeneralTab: Tab = ({\r\n  settings,\r\n  onChange,\r\n}: {\r\n  settings: SettingsState;\r\n  onChange: (changes: Partial<SettingsState>) => void;\r\n}) => {\r\n  const generalSettings = useMemo(() => settings.general, [settings]);\r\n  const form = useForm<z.infer<typeof generalFormSchema>>({\r\n    resolver: zodResolver(generalFormSchema, undefined, undefined),\r\n    defaultValues: generalSettings,\r\n    mode: \"all\",\r\n    reValidateMode: \"onBlur\",\r\n  });\r\n\r\n  const currentSettings = form.watch();\r\n  useEffect(() => {\r\n    let hasChanges = false;\r\n    for (const key in currentSettings) {\r\n      if (\r\n        currentSettings[key as keyof typeof currentSettings] !==\r\n        settings.general[key as keyof SettingsState[\"general\"]]\r\n      ) {\r\n        hasChanges = true;\r\n        break;\r\n      }\r\n    }\r\n    if (hasChanges) {\r\n      onChange({ general: currentSettings });\r\n    }\r\n  }, [currentSettings, onChange, settings]);\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <header>\r\n        <h1 className=\"text-lg font-medium\">General</h1>\r\n      </header>\r\n      <main>\r\n        <Form {...form}>\r\n          <form className=\"space-y-8\">\r\n            <FormField\r\n              control={form.control}\r\n              name=\"autoAcceptedPlan\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormControl>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Switch\r\n                        id=\"autoAcceptedPlan\"\r\n                        checked={field.value}\r\n                        onCheckedChange={field.onChange}\r\n                      />\r\n                      <Label className=\"text-sm\" htmlFor=\"autoAcceptedPlan\">\r\n                        Allow automatic acceptance of plans\r\n                      </Label>\r\n                    </div>\r\n                  </FormControl>\r\n                </FormItem>\r\n              )}\r\n            />\r\n            <FormField\r\n              control={form.control}\r\n              name=\"maxPlanIterations\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Max plan iterations</FormLabel>\r\n                  <FormControl>\r\n                    <Input\r\n                      className=\"w-60\"\r\n                      type=\"number\"\r\n                      defaultValue={field.value}\r\n                      min={1}\r\n                      onChange={(event) =>\r\n                        field.onChange(parseInt(event.target.value || \"0\"))\r\n                      }\r\n                    />\r\n                  </FormControl>\r\n                  <FormDescription>\r\n                    Set to 1 for single-step planning. Set to 2 or more to\r\n                    enable re-planning.\r\n                  </FormDescription>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n            <FormField\r\n              control={form.control}\r\n              name=\"maxStepNum\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Max steps of a research plan</FormLabel>\r\n                  <FormControl>\r\n                    <Input\r\n                      className=\"w-60\"\r\n                      type=\"number\"\r\n                      defaultValue={field.value}\r\n                      min={1}\r\n                      onChange={(event) =>\r\n                        field.onChange(parseInt(event.target.value || \"0\"))\r\n                      }\r\n                    />\r\n                  </FormControl>\r\n                  <FormDescription>\r\n                    By default, each research plan has 3 steps.\r\n                  </FormDescription>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n            <FormField\r\n              control={form.control}\r\n              name=\"maxSearchResults\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Max search results</FormLabel>\r\n                  <FormControl>\r\n                    <Input\r\n                      className=\"w-60\"\r\n                      type=\"number\"\r\n                      defaultValue={field.value}\r\n                      min={1}\r\n                      onChange={(event) =>\r\n                        field.onChange(parseInt(event.target.value || \"0\"))\r\n                      }\r\n                    />\r\n                  </FormControl>\r\n                  <FormDescription>\r\n                    By default, each search step has 3 results.\r\n                  </FormDescription>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n          </form>\r\n        </Form>\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\nGeneralTab.displayName = \"\";\r\nGeneralTab.icon = Settings;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AACA;AACA;AAEA;AASA;AACA;AACA;;;;;;;;;;;;AAKA,MAAM,oBAAoB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,kBAAkB,wLAAA,CAAA,IAAC,CAAC,OAAO;IAC3B,+BAA+B,wLAAA,CAAA,IAAC,CAAC,OAAO;IACxC,mBAAmB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACnC,SAAS;IACX;IACA,YAAY,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC5B,SAAS;IACX;IACA,kBAAkB,wLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAClC,SAAS;IACX;AACF;AAEO,MAAM,aAAkB,CAAC,EAC9B,QAAQ,EACR,QAAQ,EAIT;;IACC,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;+CAAE,IAAM,SAAS,OAAO;8CAAE;QAAC;KAAS;IAClE,MAAM,OAAO,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAqC;QACtD,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE,mBAAmB,WAAW;QACpD,eAAe;QACf,MAAM;QACN,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,KAAK,KAAK;IAClC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,aAAa;YACjB,IAAK,MAAM,OAAO,gBAAiB;gBACjC,IACE,eAAe,CAAC,IAAoC,KACpD,SAAS,OAAO,CAAC,IAAsC,EACvD;oBACA,aAAa;oBACb;gBACF;YACF;YACA,IAAI,YAAY;gBACd,SAAS;oBAAE,SAAS;gBAAgB;YACtC;QACF;+BAAG;QAAC;QAAiB;QAAU;KAAS;IAExC,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC;0BACC,cAAA,sSAAC;oBAAG,WAAU;8BAAsB;;;;;;;;;;;0BAEtC,sSAAC;0BACC,cAAA,sSAAC,mIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,sSAAC;wBAAK,WAAU;;0CACd,sSAAC,mIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,mIAAA,CAAA,WAAQ;kDACP,cAAA,sSAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,qIAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS,MAAM,KAAK;wDACpB,iBAAiB,MAAM,QAAQ;;;;;;kEAEjC,sSAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;wDAAU,SAAQ;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhE,sSAAC,mIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,mIAAA,CAAA,WAAQ;;0DACP,sSAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,sSAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,sSAAC,oIAAA,CAAA,QAAK;oDACJ,WAAU;oDACV,MAAK;oDACL,cAAc,MAAM,KAAK;oDACzB,KAAK;oDACL,UAAU,CAAC,QACT,MAAM,QAAQ,CAAC,SAAS,MAAM,MAAM,CAAC,KAAK,IAAI;;;;;;;;;;;0DAIpD,sSAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;0DAIjB,sSAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,sSAAC,mIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,mIAAA,CAAA,WAAQ;;0DACP,sSAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,sSAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,sSAAC,oIAAA,CAAA,QAAK;oDACJ,WAAU;oDACV,MAAK;oDACL,cAAc,MAAM,KAAK;oDACzB,KAAK;oDACL,UAAU,CAAC,QACT,MAAM,QAAQ,CAAC,SAAS,MAAM,MAAM,CAAC,KAAK,IAAI;;;;;;;;;;;0DAIpD,sSAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;0DAGjB,sSAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,sSAAC,mIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,sSAAC,mIAAA,CAAA,WAAQ;;0DACP,sSAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,sSAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,sSAAC,oIAAA,CAAA,QAAK;oDACJ,WAAU;oDACV,MAAK;oDACL,cAAc,MAAM,KAAK;oDACzB,KAAK;oDACL,UAAU,CAAC,QACT,MAAM,QAAQ,CAAC,SAAS,MAAM,MAAM,CAAC,KAAK,IAAI;;;;;;;;;;;0DAIpD,sSAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;0DAGjB,sSAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B;GA1Ia;;QAQE,0PAAA,CAAA,UAAO;;;KART;AA2Ib,WAAW,WAAW,GAAG;AACzB,WAAW,IAAI,GAAG,iSAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 3930, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 3961, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/mcp/schema.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { z } from \"zod\";\r\n\r\nexport const MCPConfigSchema = z.object({\r\n  mcpServers: z.record(\r\n    z.union(\r\n      [\r\n        z.object({\r\n          command: z.string({\r\n            message: \"`command` must be a string\",\r\n          }),\r\n          args: z\r\n            .array(z.string(), {\r\n              message: \"`args` must be an array of strings\",\r\n            })\r\n            .optional(),\r\n          env: z\r\n            .record(z.string(), {\r\n              message: \"`env` must be an object of key-value pairs\",\r\n            })\r\n            .optional(),\r\n        }),\r\n        z.object({\r\n          url: z\r\n            .string({\r\n              message:\r\n                \"`url` must be a valid URL starting with http:// or https://\",\r\n            })\r\n            .refine(\r\n              (value) => {\r\n                try {\r\n                  const url = new URL(value);\r\n                  return url.protocol === \"http:\" || url.protocol === \"https:\";\r\n                } catch {\r\n                  return false;\r\n                }\r\n              },\r\n              {\r\n                message:\r\n                  \"`url` must be a valid URL starting with http:// or https://\",\r\n              },\r\n            ),\r\n          env: z\r\n            .record(z.string(), {\r\n              message: \"`env` must be an object of key-value pairs\",\r\n            })\r\n            .optional(),\r\n        }),\r\n      ],\r\n      {\r\n        message: \"Invalid server type\",\r\n      },\r\n    ),\r\n  ),\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;;AAEO,MAAM,kBAAkB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,YAAY,wLAAA,CAAA,IAAC,CAAC,MAAM,CAClB,wLAAA,CAAA,IAAC,CAAC,KAAK,CACL;QACE,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,SAAS,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;gBAChB,SAAS;YACX;YACA,MAAM,wLAAA,CAAA,IAAC,CACJ,KAAK,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,IAAI;gBACjB,SAAS;YACX,GACC,QAAQ;YACX,KAAK,wLAAA,CAAA,IAAC,CACH,MAAM,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,IAAI;gBAClB,SAAS;YACX,GACC,QAAQ;QACb;QACA,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,KAAK,wLAAA,CAAA,IAAC,CACH,MAAM,CAAC;gBACN,SACE;YACJ,GACC,MAAM,CACL,CAAC;gBACC,IAAI;oBACF,MAAM,MAAM,IAAI,IAAI;oBACpB,OAAO,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK;gBACtD,EAAE,OAAM;oBACN,OAAO;gBACT;YACF,GACA;gBACE,SACE;YACJ;YAEJ,KAAK,wLAAA,CAAA,IAAC,CACH,MAAM,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM,IAAI;gBAClB,SAAS;YACX,GACC,QAAQ;QACb;KACD,EACD;QACE,SAAS;IACX;AAGN", "debugId": null}}, {"offset": {"line": 4011, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/mcp/types.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport interface MCPToolMetadata {\r\n  name: string;\r\n  description: string;\r\n  inputSchema?: Record<string, unknown>;\r\n}\r\n\r\nexport interface GenericMCPServerMetadata<T extends string> {\r\n  name: string;\r\n  transport: T;\r\n  enabled: boolean;\r\n  env?: Record<string, string>;\r\n  tools: MCPToolMetadata[];\r\n  createdAt: number;\r\n  updatedAt: number;\r\n}\r\n\r\nexport interface StdioMCPServerMetadata\r\n  extends GenericMCPServerMetadata<\"stdio\"> {\r\n  transport: \"stdio\";\r\n  command: string;\r\n  args?: string[];\r\n}\r\nexport type SimpleStdioMCPServerMetadata = Omit<\r\n  StdioMCPServerMetadata,\r\n  \"enabled\" | \"tools\" | \"createdAt\" | \"updatedAt\"\r\n>;\r\n\r\nexport interface SSEMCPServerMetadata extends GenericMCPServerMetadata<\"sse\"> {\r\n  transport: \"sse\";\r\n  url: string;\r\n}\r\nexport type SimpleSSEMCPServerMetadata = Omit<\r\n  SSEMCPServerMetadata,\r\n  \"enabled\" | \"tools\" | \"createdAt\" | \"updatedAt\"\r\n>;\r\n\r\nexport type MCPServerMetadata = StdioMCPServerMetadata | SSEMCPServerMetadata;\r\nexport type SimpleMCPServerMetadata =\r\n  | SimpleStdioMCPServerMetadata\r\n  | SimpleSSEMCPServerMetadata;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B", "debugId": null}}, {"offset": {"line": 4024, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/mcp/utils.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useSettingsStore } from \"../store\";\r\n\r\nexport function findMCPTool(name: string) {\r\n  const mcpServers = useSettingsStore.getState().mcp.servers;\r\n  for (const server of mcpServers) {\r\n    for (const tool of server.tools) {\r\n      if (tool.name === name) {\r\n        return tool;\r\n      }\r\n    }\r\n  }\r\n  return null;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AAAA;;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,aAAa,4IAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO;IAC1D,KAAK,MAAM,UAAU,WAAY;QAC/B,KAAK,MAAM,QAAQ,OAAO,KAAK,CAAE;YAC/B,IAAI,KAAK,IAAI,KAAK,MAAM;gBACtB,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4052, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/mcp/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./schema\";\r\nexport * from \"./types\";\r\nexport * from \"./utils\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B;AACA;AACA", "debugId": null}}, {"offset": {"line": 4081, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/settings/dialogs/add-mcp-server-dialog.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { useCallback, useState } from \"react\";\r\n\r\nimport { Button } from \"~/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"~/components/ui/dialog\";\r\nimport { Textarea } from \"~/components/ui/textarea\";\r\nimport { queryMCPServerMetadata } from \"~/core/api\";\r\nimport {\r\n  MCPConfigSchema,\r\n  type MCPServerMetadata,\r\n  type SimpleMCPServerMetadata,\r\n  type SimpleSSEMCPServerMetadata,\r\n  type SimpleStdioMCPServerMetadata,\r\n} from \"~/core/mcp\";\r\n\r\nexport function AddMCPServerDialog({\r\n  onAdd,\r\n}: {\r\n  onAdd?: (servers: MCPServerMetadata[]) => void;\r\n}) {\r\n  const [open, setOpen] = useState(false);\r\n  const [input, setInput] = useState(\"\");\r\n  const [validationError, setValidationError] = useState<string | null>(\"\");\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [processing, setProcessing] = useState(false);\r\n  const handleChange = useCallback((value: string) => {\r\n    setInput(value);\r\n    if (!value.trim()) {\r\n      setValidationError(null);\r\n      return;\r\n    }\r\n    setValidationError(null);\r\n    try {\r\n      const parsed = JSON.parse(value);\r\n      if (!(\"mcpServers\" in parsed)) {\r\n        setValidationError(\"Missing `mcpServers` in JSON\");\r\n        return;\r\n      }\r\n    } catch {\r\n      setValidationError(\"Invalid JSON\");\r\n      return;\r\n    }\r\n    const result = MCPConfigSchema.safeParse(JSON.parse(value));\r\n    if (!result.success) {\r\n      if (result.error.errors[0]) {\r\n        const error = result.error.errors[0];\r\n        if (error.code === \"invalid_union\") {\r\n          if (error.unionErrors[0]?.errors[0]) {\r\n            setValidationError(error.unionErrors[0].errors[0].message);\r\n            return;\r\n          }\r\n        }\r\n      }\r\n      const errorMessage =\r\n        result.error.errors[0]?.message ?? \"Validation failed\";\r\n      setValidationError(errorMessage);\r\n      return;\r\n    }\r\n\r\n    const keys = Object.keys(result.data.mcpServers);\r\n    if (keys.length === 0) {\r\n      setValidationError(\"Missing server name in `mcpServers`\");\r\n      return;\r\n    }\r\n  }, []);\r\n  const handleAdd = useCallback(async () => {\r\n    const config = MCPConfigSchema.parse(JSON.parse(input));\r\n    setInput(JSON.stringify(config, null, 2));\r\n    const addingServers: SimpleMCPServerMetadata[] = [];\r\n    for (const [key, server] of Object.entries(config.mcpServers)) {\r\n      if (\"command\" in server) {\r\n        const metadata: SimpleStdioMCPServerMetadata = {\r\n          transport: \"stdio\",\r\n          name: key,\r\n          command: server.command,\r\n          args: server.args,\r\n          env: server.env,\r\n        };\r\n        addingServers.push(metadata);\r\n      } else if (\"url\" in server) {\r\n        const metadata: SimpleSSEMCPServerMetadata = {\r\n          transport: \"sse\",\r\n          name: key,\r\n          url: server.url,\r\n        };\r\n        addingServers.push(metadata);\r\n      }\r\n    }\r\n    setProcessing(true);\r\n\r\n    const results: MCPServerMetadata[] = [];\r\n    let processingServer: string | null = null;\r\n    try {\r\n      setError(null);\r\n      for (const server of addingServers) {\r\n        processingServer = server.name;\r\n        const metadata = await queryMCPServerMetadata(server);\r\n        results.push({ ...metadata, name: server.name, enabled: true });\r\n      }\r\n      if (results.length > 0) {\r\n        onAdd?.(results);\r\n      }\r\n      setInput(\"\");\r\n      setOpen(false);\r\n    } catch (e) {\r\n      console.error(e);\r\n      setError(`Failed to add server: ${processingServer}`);\r\n    } finally {\r\n      setProcessing(false);\r\n    }\r\n  }, [input, onAdd]);\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={setOpen}>\r\n      <DialogTrigger asChild>\r\n        <Button size=\"sm\">Add Servers</Button>\r\n      </DialogTrigger>\r\n      <DialogContent className=\"sm:max-w-[560px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Add New MCP Servers</DialogTitle>\r\n        </DialogHeader>\r\n        <DialogDescription>\r\n          DeerFlow uses the standard JSON MCP config to create a new server.\r\n          <br />\r\n          Paste your config below and click &quot;Add&quot; to add new servers.\r\n        </DialogDescription>\r\n\r\n        <main>\r\n          <Textarea\r\n            className=\"h-[360px]\"\r\n            placeholder={\r\n              'Example:\\n\\n{\\n  \"mcpServers\": {\\n    \"My Server\": {\\n      \"command\": \"python\",\\n      \"args\": [\\n        \"-m\", \"mcp_server\"\\n      ],\\n      \"env\": {\\n        \"API_KEY\": \"YOUR_API_KEY\"\\n      }\\n    }\\n  }\\n}'\r\n            }\r\n            value={input}\r\n            onChange={(e) => handleChange(e.target.value)}\r\n          />\r\n        </main>\r\n\r\n        <DialogFooter>\r\n          <div className=\"flex h-10 w-full items-center justify-between gap-2\">\r\n            <div className=\"text-destructive flex-grow overflow-hidden text-sm\">\r\n              {validationError ?? error}\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button variant=\"outline\" onClick={() => setOpen(false)}>\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                className=\"w-24\"\r\n                type=\"submit\"\r\n                disabled={!input.trim() || !!validationError || processing}\r\n                onClick={handleAdd}\r\n              >\r\n                {processing && <Loader2 className=\"animate-spin\" />}\r\n                Add\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAEA;AACA;AASA;AACA;AAAA;AACA;AAAA;;;;;;;;;;AAQO,SAAS,mBAAmB,EACjC,KAAK,EAGN;;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAChC,SAAS;YACT,IAAI,CAAC,MAAM,IAAI,IAAI;gBACjB,mBAAmB;gBACnB;YACF;YACA,mBAAmB;YACnB,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,CAAC,CAAC,gBAAgB,MAAM,GAAG;oBAC7B,mBAAmB;oBACnB;gBACF;YACF,EAAE,OAAM;gBACN,mBAAmB;gBACnB;YACF;YACA,MAAM,SAAS,+HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC;YACpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE;oBAC1B,MAAM,QAAQ,OAAO,KAAK,CAAC,MAAM,CAAC,EAAE;oBACpC,IAAI,MAAM,IAAI,KAAK,iBAAiB;wBAClC,IAAI,MAAM,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;4BACnC,mBAAmB,MAAM,WAAW,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;4BACzD;wBACF;oBACF;gBACF;gBACA,MAAM,eACJ,OAAO,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW;gBACrC,mBAAmB;gBACnB;YACF;YAEA,MAAM,OAAO,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU;YAC/C,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,mBAAmB;gBACnB;YACF;QACF;uDAAG,EAAE;IACL,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;qDAAE;YAC5B,MAAM,SAAS,+HAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;YAChD,SAAS,KAAK,SAAS,CAAC,QAAQ,MAAM;YACtC,MAAM,gBAA2C,EAAE;YACnD,KAAK,MAAM,CAAC,KAAK,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,UAAU,EAAG;gBAC7D,IAAI,aAAa,QAAQ;oBACvB,MAAM,WAAyC;wBAC7C,WAAW;wBACX,MAAM;wBACN,SAAS,OAAO,OAAO;wBACvB,MAAM,OAAO,IAAI;wBACjB,KAAK,OAAO,GAAG;oBACjB;oBACA,cAAc,IAAI,CAAC;gBACrB,OAAO,IAAI,SAAS,QAAQ;oBAC1B,MAAM,WAAuC;wBAC3C,WAAW;wBACX,MAAM;wBACN,KAAK,OAAO,GAAG;oBACjB;oBACA,cAAc,IAAI,CAAC;gBACrB;YACF;YACA,cAAc;YAEd,MAAM,UAA+B,EAAE;YACvC,IAAI,mBAAkC;YACtC,IAAI;gBACF,SAAS;gBACT,KAAK,MAAM,UAAU,cAAe;oBAClC,mBAAmB,OAAO,IAAI;oBAC9B,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE;oBAC9C,QAAQ,IAAI,CAAC;wBAAE,GAAG,QAAQ;wBAAE,MAAM,OAAO,IAAI;wBAAE,SAAS;oBAAK;gBAC/D;gBACA,IAAI,QAAQ,MAAM,GAAG,GAAG;oBACtB,QAAQ;gBACV;gBACA,SAAS;gBACT,QAAQ;YACV,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC;gBACd,SAAS,CAAC,sBAAsB,EAAE,kBAAkB;YACtD,SAAU;gBACR,cAAc;YAChB;QACF;oDAAG;QAAC;QAAO;KAAM;IAEjB,qBACE,sSAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBAAC,MAAK;8BAAK;;;;;;;;;;;0BAEpB,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,sSAAC,qIAAA,CAAA,eAAY;kCACX,cAAA,sSAAC,qIAAA,CAAA,cAAW;sCAAC;;;;;;;;;;;kCAEf,sSAAC,qIAAA,CAAA,oBAAiB;;4BAAC;0CAEjB,sSAAC;;;;;4BAAK;;;;;;;kCAIR,sSAAC;kCACC,cAAA,sSAAC,uIAAA,CAAA,WAAQ;4BACP,WAAU;4BACV,aACE;4BAEF,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;kCAIhD,sSAAC,qIAAA,CAAA,eAAY;kCACX,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;8CACZ,mBAAmB;;;;;;8CAEtB,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,QAAQ;sDAAQ;;;;;;sDAGzD,sSAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,MAAK;4CACL,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,mBAAmB;4CAChD,SAAS;;gDAER,4BAAc,sSAAC,wSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;GAnJgB;KAAA", "debugId": null}}, {"offset": {"line": 4359, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/settings/tabs/mcp-tab.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Blocks, PencilRuler, Trash } from \"lucide-react\";\r\nimport { useCallback, useState } from \"react\";\r\n\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Switch } from \"~/components/ui/switch\";\r\nimport type { MCPServerMetadata } from \"~/core/mcp\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { AddMCPServerDialog } from \"../dialogs/add-mcp-server-dialog\";\r\n\r\nimport type { Tab } from \"./types\";\r\n\r\nexport const MCPTab: Tab = ({ settings, onChange }) => {\r\n  const [servers, setServers] = useState<MCPServerMetadata[]>(\r\n    settings.mcp.servers,\r\n  );\r\n  const [newlyAdded, setNewlyAdded] = useState(false);\r\n  const handleAddServers = useCallback(\r\n    (servers: MCPServerMetadata[]) => {\r\n      const merged = mergeServers(settings.mcp.servers, servers);\r\n      setServers(merged);\r\n      onChange({ ...settings, mcp: { ...settings.mcp, servers: merged } });\r\n      setNewlyAdded(true);\r\n      setTimeout(() => {\r\n        setNewlyAdded(false);\r\n      }, 1000);\r\n      setTimeout(() => {\r\n        document.getElementById(\"settings-content-scrollable\")?.scrollTo({\r\n          top: 0,\r\n          behavior: \"smooth\",\r\n        });\r\n      }, 100);\r\n    },\r\n    [onChange, settings],\r\n  );\r\n  const handleDeleteServer = useCallback(\r\n    (name: string) => {\r\n      const merged = settings.mcp.servers.filter(\r\n        (server) => server.name !== name,\r\n      );\r\n      setServers(merged);\r\n      onChange({ ...settings, mcp: { ...settings.mcp, servers: merged } });\r\n    },\r\n    [onChange, settings],\r\n  );\r\n  const handleToggleServer = useCallback(\r\n    (name: string, enabled: boolean) => {\r\n      const merged = settings.mcp.servers.map((server) =>\r\n        server.name === name ? { ...server, enabled } : server,\r\n      );\r\n      setServers(merged);\r\n      onChange({ ...settings, mcp: { ...settings.mcp, servers: merged } });\r\n    },\r\n    [onChange, settings],\r\n  );\r\n  const animationProps = {\r\n    initial: { backgroundColor: \"gray\" },\r\n    animate: { backgroundColor: \"transparent\" },\r\n    transition: { duration: 1 },\r\n    style: {\r\n      transition: \"background-color 1s ease-out\",\r\n    },\r\n  };\r\n  return (\r\n    <div className=\"flex flex-col gap-4\">\r\n      <header>\r\n        <div className=\"flex items-center justify-between gap-2\">\r\n          <h1 className=\"text-lg font-medium\">MCP Servers</h1>\r\n          <AddMCPServerDialog onAdd={handleAddServers} />\r\n        </div>\r\n        <div className=\"text-muted-foreground markdown text-sm\">\r\n          The Model Context Protocol boosts DeerFlow by integrating external\r\n          tools for tasks like private domain searches, web browsing, food\r\n          ordering, and more. Click here to\r\n          <a\r\n            className=\"ml-1\"\r\n            target=\"_blank\"\r\n            href=\"https://modelcontextprotocol.io/\"\r\n          >\r\n            learn more about MCP.\r\n          </a>\r\n        </div>\r\n      </header>\r\n      <main>\r\n        <ul id=\"mcp-servers-list\" className=\"flex flex-col gap-4\">\r\n          {servers.map((server) => {\r\n            const isNew =\r\n              server.createdAt &&\r\n              server.createdAt > Date.now() - 1000 * 60 * 60 * 1;\r\n            return (\r\n              <motion.li\r\n                className={\r\n                  \"!bg-card group relative overflow-hidden rounded-lg border pb-2 shadow duration-300\"\r\n                }\r\n                key={server.name}\r\n                {...(isNew && newlyAdded && animationProps)}\r\n              >\r\n                <div className=\"absolute top-3 right-2\">\r\n                  <Tooltip title=\"Enable/disable server\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Switch\r\n                        id=\"airplane-mode\"\r\n                        checked={server.enabled}\r\n                        onCheckedChange={(checked) => {\r\n                          handleToggleServer(server.name, checked);\r\n                        }}\r\n                      />\r\n                    </div>\r\n                  </Tooltip>\r\n                </div>\r\n                <div className=\"absolute top-1 right-12 opacity-0 transition-opacity duration-300 group-hover:opacity-100\">\r\n                  <Tooltip title=\"Delete server\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      onClick={() => handleDeleteServer(server.name)}\r\n                    >\r\n                      <Trash />\r\n                    </Button>\r\n                  </Tooltip>\r\n                </div>\r\n                <div\r\n                  className={cn(\r\n                    \"flex flex-col items-start px-4 py-2\",\r\n                    !server.enabled && \"text-muted-foreground\",\r\n                  )}\r\n                >\r\n                  <div\r\n                    className={cn(\r\n                      \"mb-2 flex items-center gap-2\",\r\n                      !server.enabled && \"opacity-70\",\r\n                    )}\r\n                  >\r\n                    <div className=\"text-lg font-medium\">{server.name}</div>\r\n                    {!server.enabled && (\r\n                      <div className=\"bg-primary text-primary-foreground h-fit rounded px-1.5 py-0.5 text-xs\">\r\n                        Disabled\r\n                      </div>\r\n                    )}\r\n                    <div className=\"bg-primary text-primary-foreground h-fit rounded px-1.5 py-0.5 text-xs\">\r\n                      {server.transport}\r\n                    </div>\r\n                    {isNew && (\r\n                      <div className=\"bg-primary text-primary-foreground h-fit rounded px-1.5 py-0.5 text-xs\">\r\n                        New\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  <ul\r\n                    className={cn(\r\n                      \"flex flex-wrap items-center gap-2\",\r\n                      !server.enabled && \"opacity-70\",\r\n                    )}\r\n                  >\r\n                    <PencilRuler size={16} />\r\n                    {server.tools.map((tool) => (\r\n                      <li\r\n                        key={tool.name}\r\n                        className=\"text-muted-foreground border-muted-foreground w-fit rounded-md border px-2\"\r\n                      >\r\n                        <Tooltip key={tool.name} title={tool.description}>\r\n                          <div className=\"w-fit text-sm\">{tool.name}</div>\r\n                        </Tooltip>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n              </motion.li>\r\n            );\r\n          })}\r\n        </ul>\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\nMCPTab.icon = Blocks;\r\nMCPTab.badge = \"Beta\";\r\n\r\nfunction mergeServers(\r\n  existing: MCPServerMetadata[],\r\n  added: MCPServerMetadata[],\r\n): MCPServerMetadata[] {\r\n  const serverMap = new Map(existing.map((server) => [server.name, server]));\r\n\r\n  for (const addedServer of added) {\r\n    addedServer.createdAt = Date.now();\r\n    addedServer.updatedAt = Date.now();\r\n    serverMap.set(addedServer.name, addedServer);\r\n  }\r\n\r\n  const result = Array.from(serverMap.values());\r\n  result.sort((a, b) => b.createdAt - a.createdAt);\r\n  return result;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AAEA;;;;;;;;;;;AAIO,MAAM,SAAc,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE;;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EACnC,SAAS,GAAG,CAAC,OAAO;IAEtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,mBAAmB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDACjC,CAAC;YACC,MAAM,SAAS,aAAa,SAAS,GAAG,CAAC,OAAO,EAAE;YAClD,WAAW;YACX,SAAS;gBAAE,GAAG,QAAQ;gBAAE,KAAK;oBAAE,GAAG,SAAS,GAAG;oBAAE,SAAS;gBAAO;YAAE;YAClE,cAAc;YACd;wDAAW;oBACT,cAAc;gBAChB;uDAAG;YACH;wDAAW;oBACT,SAAS,cAAc,CAAC,gCAAgC,SAAS;wBAC/D,KAAK;wBACL,UAAU;oBACZ;gBACF;uDAAG;QACL;+CACA;QAAC;QAAU;KAAS;IAEtB,MAAM,qBAAqB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDACnC,CAAC;YACC,MAAM,SAAS,SAAS,GAAG,CAAC,OAAO,CAAC,MAAM;iEACxC,CAAC,SAAW,OAAO,IAAI,KAAK;;YAE9B,WAAW;YACX,SAAS;gBAAE,GAAG,QAAQ;gBAAE,KAAK;oBAAE,GAAG,SAAS,GAAG;oBAAE,SAAS;gBAAO;YAAE;QACpE;iDACA;QAAC;QAAU;KAAS;IAEtB,MAAM,qBAAqB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDACnC,CAAC,MAAc;YACb,MAAM,SAAS,SAAS,GAAG,CAAC,OAAO,CAAC,GAAG;iEAAC,CAAC,SACvC,OAAO,IAAI,KAAK,OAAO;wBAAE,GAAG,MAAM;wBAAE;oBAAQ,IAAI;;YAElD,WAAW;YACX,SAAS;gBAAE,GAAG,QAAQ;gBAAE,KAAK;oBAAE,GAAG,SAAS,GAAG;oBAAE,SAAS;gBAAO;YAAE;QACpE;iDACA;QAAC;QAAU;KAAS;IAEtB,MAAM,iBAAiB;QACrB,SAAS;YAAE,iBAAiB;QAAO;QACnC,SAAS;YAAE,iBAAiB;QAAc;QAC1C,YAAY;YAAE,UAAU;QAAE;QAC1B,OAAO;YACL,YAAY;QACd;IACF;IACA,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC;;kCACC,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,sSAAC,uKAAA,CAAA,qBAAkB;gCAAC,OAAO;;;;;;;;;;;;kCAE7B,sSAAC;wBAAI,WAAU;;4BAAyC;0CAItD,sSAAC;gCACC,WAAU;gCACV,QAAO;gCACP,MAAK;0CACN;;;;;;;;;;;;;;;;;;0BAKL,sSAAC;0BACC,cAAA,sSAAC;oBAAG,IAAG;oBAAmB,WAAU;8BACjC,QAAQ,GAAG,CAAC,CAAC;wBACZ,MAAM,QACJ,OAAO,SAAS,IAChB,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK;wBACnD,qBACE,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WACE;4BAGD,GAAI,SAAS,cAAc,cAAc;;8CAE1C,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC,gJAAA,CAAA,UAAO;wCAAC,OAAM;kDACb,cAAA,sSAAC;4CAAI,WAAU;sDACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,OAAO,OAAO;gDACvB,iBAAiB,CAAC;oDAChB,mBAAmB,OAAO,IAAI,EAAE;gDAClC;;;;;;;;;;;;;;;;;;;;;8CAKR,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC,gJAAA,CAAA,UAAO;wCAAC,OAAM;kDACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,mBAAmB,OAAO,IAAI;sDAE7C,cAAA,sSAAC,2RAAA,CAAA,QAAK;;;;;;;;;;;;;;;;;;;;8CAIZ,sSAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uCACA,CAAC,OAAO,OAAO,IAAI;;sDAGrB,sSAAC;4CACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,CAAC,OAAO,OAAO,IAAI;;8DAGrB,sSAAC;oDAAI,WAAU;8DAAuB,OAAO,IAAI;;;;;;gDAChD,CAAC,OAAO,OAAO,kBACd,sSAAC;oDAAI,WAAU;8DAAyE;;;;;;8DAI1F,sSAAC;oDAAI,WAAU;8DACZ,OAAO,SAAS;;;;;;gDAElB,uBACC,sSAAC;oDAAI,WAAU;8DAAyE;;;;;;;;;;;;sDAK5F,sSAAC;4CACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,CAAC,OAAO,OAAO,IAAI;;8DAGrB,sSAAC,2SAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;gDAClB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,qBACjB,sSAAC;wDAEC,WAAU;kEAEV,cAAA,sSAAC,gJAAA,CAAA,UAAO;4DAAiB,OAAO,KAAK,WAAW;sEAC9C,cAAA,sSAAC;gEAAI,WAAU;0EAAiB,KAAK,IAAI;;;;;;2DAD7B,KAAK,IAAI;;;;;uDAHlB,KAAK,IAAI;;;;;;;;;;;;;;;;;;2BA/DjB,OAAO,IAAI;;;;;oBA2EtB;;;;;;;;;;;;;;;;;AAKV;GAlKa;KAAA;AAmKb,OAAO,IAAI,GAAG,6RAAA,CAAA,SAAM;AACpB,OAAO,KAAK,GAAG;AAEf,SAAS,aACP,QAA6B,EAC7B,KAA0B;IAE1B,MAAM,YAAY,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,SAAW;YAAC,OAAO,IAAI;YAAE;SAAO;IAExE,KAAK,MAAM,eAAe,MAAO;QAC/B,YAAY,SAAS,GAAG,KAAK,GAAG;QAChC,YAAY,SAAS,GAAG,KAAK,GAAG;QAChC,UAAU,GAAG,CAAC,YAAY,IAAI,EAAE;IAClC;IAEA,MAAM,SAAS,MAAM,IAAI,CAAC,UAAU,MAAM;IAC1C,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IAC/C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4738, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/settings/tabs/index.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Settings, type LucideIcon } from \"lucide-react\";\r\n\r\nimport { AboutTab } from \"./about-tab\";\r\nimport { GeneralTab } from \"./general-tab\";\r\nimport { MCPTab } from \"./mcp-tab\";\r\n\r\nexport const SETTINGS_TABS = [GeneralTab, MCPTab, AboutTab].map((tab) => {\r\n  const name = tab.name ?? tab.displayName;\r\n  return {\r\n    ...tab,\r\n    id: name.replace(/Tab$/, \"\").toLocaleLowerCase(),\r\n    label: name.replace(/Tab$/, \"\"),\r\n    icon: (tab.icon ?? <Settings />) as LucideIcon,\r\n    component: tab,\r\n  };\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AACA;AACA;;;;;;AAEO,MAAM,gBAAgB;IAAC,oJAAA,CAAA,aAAU;IAAE,gJAAA,CAAA,SAAM;IAAE,kJAAA,CAAA,WAAQ;CAAC,CAAC,GAAG,MAAC,CAAC;IAC/D,MAAM,OAAO,IAAI,IAAI,IAAI,IAAI,WAAW;IACxC,OAAO;QACL,GAAG,GAAG;QACN,IAAI,KAAK,OAAO,CAAC,QAAQ,IAAI,iBAAiB;QAC9C,OAAO,KAAK,OAAO,CAAC,QAAQ;QAC5B,MAAO,IAAI,IAAI,kBAAI,sSAAC,iSAAA,CAAA,WAAQ;;;;;QAC5B,WAAW;IACb;AACF", "debugId": null}}, {"offset": {"line": 4784, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/settings/dialogs/settings-dialog.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Settings } from \"lucide-react\";\r\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\r\n\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport { Badge } from \"~/components/ui/badge\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"~/components/ui/dialog\";\r\nimport { Tabs, TabsContent } from \"~/components/ui/tabs\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport {\r\n  type SettingsState,\r\n  changeSettings,\r\n  saveSettings,\r\n  useSettingsStore,\r\n} from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { SETTINGS_TABS } from \"../tabs\";\r\n\r\nexport function SettingsDialog() {\r\n  const { isReplay } = useReplay();\r\n  const [activeTabId, setActiveTabId] = useState(SETTINGS_TABS[0]!.id);\r\n  const [open, setOpen] = useState(false);\r\n  const [settings, setSettings] = useState(useSettingsStore.getState());\r\n  const [changes, setChanges] = useState<Partial<SettingsState>>({});\r\n\r\n  const handleTabChange = useCallback(\r\n    (newChanges: Partial<SettingsState>) => {\r\n      setTimeout(() => {\r\n        if (open) {\r\n          setChanges((prev) => ({\r\n            ...prev,\r\n            ...newChanges,\r\n          }));\r\n        }\r\n      }, 0);\r\n    },\r\n    [open],\r\n  );\r\n\r\n  const handleSave = useCallback(() => {\r\n    if (Object.keys(changes).length > 0) {\r\n      const newSettings: SettingsState = {\r\n        ...settings,\r\n        ...changes,\r\n      };\r\n      setSettings(newSettings);\r\n      setChanges({});\r\n      changeSettings(newSettings);\r\n      saveSettings();\r\n    }\r\n    setOpen(false);\r\n  }, [settings, changes]);\r\n\r\n  const handleOpen = useCallback(() => {\r\n    setSettings(useSettingsStore.getState());\r\n  }, []);\r\n\r\n  const handleClose = useCallback(() => {\r\n    setChanges({});\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (open) {\r\n      handleOpen();\r\n    } else {\r\n      handleClose();\r\n    }\r\n  }, [open, handleOpen, handleClose]);\r\n\r\n  const mergedSettings = useMemo<SettingsState>(() => {\r\n    return {\r\n      ...settings,\r\n      ...changes,\r\n    };\r\n  }, [settings, changes]);\r\n\r\n  if (isReplay) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={setOpen}>\r\n      <Tooltip title=\"Settings\">\r\n        <DialogTrigger asChild>\r\n          <Button variant=\"ghost\" size=\"icon\">\r\n            <Settings />\r\n          </Button>\r\n        </DialogTrigger>\r\n      </Tooltip>\r\n      <DialogContent className=\"sm:max-w-[850px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>DeerFlow Settings</DialogTitle>\r\n          <DialogDescription>\r\n            Manage your DeerFlow settings here.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <Tabs value={activeTabId}>\r\n          <div className=\"flex h-120 w-full overflow-auto border-y\">\r\n            <ul className=\"flex w-50 shrink-0 border-r p-1\">\r\n              <div className=\"size-full\">\r\n                {SETTINGS_TABS.map((tab) => (\r\n                  <li\r\n                    key={tab.id}\r\n                    className={cn(\r\n                      \"hover:accent-foreground hover:bg-accent mb-1 flex h-8 w-full cursor-pointer items-center gap-1.5 rounded px-2\",\r\n                      activeTabId === tab.id &&\r\n                        \"!bg-primary !text-primary-foreground\",\r\n                    )}\r\n                    onClick={() => setActiveTabId(tab.id)}\r\n                  >\r\n                    <tab.icon size={16} />\r\n                    <span>{tab.label}</span>\r\n                    {tab.badge && (\r\n                      <Badge\r\n                        variant=\"outline\"\r\n                        className={cn(\r\n                          \"border-muted-foreground text-muted-foreground ml-auto px-1 py-0 text-xs\",\r\n                          activeTabId === tab.id &&\r\n                            \"border-primary-foreground text-primary-foreground\",\r\n                        )}\r\n                      >\r\n                        {tab.badge}\r\n                      </Badge>\r\n                    )}\r\n                  </li>\r\n                ))}\r\n              </div>\r\n            </ul>\r\n            <div className=\"min-w-0 flex-grow\">\r\n              <div\r\n                id=\"settings-content-scrollable\"\r\n                className=\"size-full overflow-auto p-4\"\r\n              >\r\n                {SETTINGS_TABS.map((tab) => (\r\n                  <TabsContent key={tab.id} value={tab.id}>\r\n                    <tab.component\r\n                      settings={mergedSettings}\r\n                      onChange={handleTabChange}\r\n                    />\r\n                  </TabsContent>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </Tabs>\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={() => setOpen(false)}>\r\n            Cancel\r\n          </Button>\r\n          <Button className=\"w-24\" type=\"submit\" onClick={handleSave}>\r\n            Save\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAEA;AACA;AACA;AACA;AASA;AACA;AAAA;AACA;AAAA;AAMA;AAEA;;;;;;;;;;;;;;AAEO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE,2IAAA,CAAA,gBAAa,CAAC,EAAE,CAAE,EAAE;IACnE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE,4IAAA,CAAA,mBAAgB,CAAC,QAAQ;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEhE,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;uDAChC,CAAC;YACC;+DAAW;oBACT,IAAI,MAAM;wBACR;2EAAW,CAAC,OAAS,CAAC;oCACpB,GAAG,IAAI;oCACP,GAAG,UAAU;gCACf,CAAC;;oBACH;gBACF;8DAAG;QACL;sDACA;QAAC;KAAK;IAGR,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B,IAAI,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;gBACnC,MAAM,cAA6B;oBACjC,GAAG,QAAQ;oBACX,GAAG,OAAO;gBACZ;gBACA,YAAY;gBACZ,WAAW,CAAC;gBACZ,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD,EAAE;gBACf,CAAA,GAAA,4IAAA,CAAA,eAAY,AAAD;YACb;YACA,QAAQ;QACV;iDAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B,YAAY,4IAAA,CAAA,mBAAgB,CAAC,QAAQ;QACvC;iDAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAAE;YAC9B,WAAW,CAAC;QACd;kDAAG,EAAE;IAEL,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,MAAM;gBACR;YACF,OAAO;gBACL;YACF;QACF;mCAAG;QAAC;QAAM;QAAY;KAAY;IAElC,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;kDAAiB;YAC5C,OAAO;gBACL,GAAG,QAAQ;gBACX,GAAG,OAAO;YACZ;QACF;iDAAG;QAAC;QAAU;KAAQ;IAEtB,IAAI,UAAU;QACZ,OAAO;IACT;IAEA,qBACE,sSAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,sSAAC,gJAAA,CAAA,UAAO;gBAAC,OAAM;0BACb,cAAA,sSAAC,qIAAA,CAAA,gBAAa;oBAAC,OAAO;8BACpB,cAAA,sSAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;kCAC3B,cAAA,sSAAC,iSAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;0BAIf,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,sSAAC,qIAAA,CAAA,eAAY;;0CACX,sSAAC,qIAAA,CAAA,cAAW;0CAAC;;;;;;0CACb,sSAAC,qIAAA,CAAA,oBAAiB;0CAAC;;;;;;;;;;;;kCAIrB,sSAAC,mIAAA,CAAA,OAAI;wBAAC,OAAO;kCACX,cAAA,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAG,WAAU;8CACZ,cAAA,sSAAC;wCAAI,WAAU;kDACZ,2IAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,oBAClB,sSAAC;gDAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iHACA,gBAAgB,IAAI,EAAE,IACpB;gDAEJ,SAAS,IAAM,eAAe,IAAI,EAAE;;kEAEpC,sSAAC,IAAI,IAAI;wDAAC,MAAM;;;;;;kEAChB,sSAAC;kEAAM,IAAI,KAAK;;;;;;oDACf,IAAI,KAAK,kBACR,sSAAC,oIAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2EACA,gBAAgB,IAAI,EAAE,IACpB;kEAGH,IAAI,KAAK;;;;;;;+CAnBT,IAAI,EAAE;;;;;;;;;;;;;;;8CA0BnB,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC;wCACC,IAAG;wCACH,WAAU;kDAET,2IAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,oBAClB,sSAAC,mIAAA,CAAA,cAAW;gDAAc,OAAO,IAAI,EAAE;0DACrC,cAAA,sSAAC,IAAI,SAAS;oDACZ,UAAU;oDACV,UAAU;;;;;;+CAHI,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWlC,sSAAC,qIAAA,CAAA,eAAY;;0CACX,sSAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,QAAQ;0CAAQ;;;;;;0CAGzD,sSAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAO,MAAK;gCAAS,SAAS;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOtE;GA1IgB;;QACO,iIAAA,CAAA,YAAS;;;KADhB", "debugId": null}}, {"offset": {"line": 5102, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/page.tsx"], "sourcesContent": ["// web/src/app/chat/page.tsx\r\n// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { GithubOutlined } from \"@ant-design/icons\";\r\nimport dynamic from \"next/dynamic\";\r\nimport Link from \"next/link\";\r\nimport { Suspense } from \"react\";\r\n\r\nimport { Button } from \"~/components/ui/button\";\r\n\r\nimport { Logo } from \"../../components/deer-flow/logo\";\r\nimport { ThemeToggle } from \"../../components/deer-flow/theme-toggle\";\r\nimport { Tooltip } from \"../../components/deer-flow/tooltip\";\r\nimport { SettingsDialog } from \"../settings/dialogs/settings-dialog\";\r\n\r\nconst Main = dynamic(() => import(\"./main\"), {\r\n  ssr: false,\r\n  loading: () => (\r\n    <div className=\"flex h-full w-full items-center justify-center\">\r\n      Loading DeerFlow...\r\n    </div>\r\n  ),\r\n});\r\n\r\nexport default function HomePage() {\r\n  return (\r\n    <div className=\"flex h-screen w-screen justify-center overscroll-none\">\r\n      <header className=\"fixed top-0 left-0 flex h-12 w-full items-center justify-between px-4\">\r\n        <Logo />\r\n        <div className=\"flex items-center gap-2\">\r\n          <Tooltip title=\"Star DeerFlow on GitHub\">\r\n            <Button variant=\"ghost\" size=\"icon\" asChild>\r\n              <Link\r\n                href=\"https://github.com/bytedance/deer-flow\"\r\n                target=\"_blank\"\r\n              >\r\n                <GithubOutlined />\r\n              </Link>\r\n            </Button>\r\n          </Tooltip>\r\n          <ThemeToggle />\r\n          <Suspense>\r\n            <SettingsDialog />\r\n          </Suspense>\r\n        </div>\r\n      </header>\r\n      <Main />\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": "AAAA,4BAA4B;AAC5B,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;;AAZA;;;;;;;;;;;AAcA,MAAM,OAAO,CAAA,GAAA,2QAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACnB,KAAK;IACL,SAAS,kBACP,sSAAC;YAAI,WAAU;sBAAiD;;;;;;;KAH9D;AASS,SAAS;IACtB,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC;gBAAO,WAAU;;kCAChB,sSAAC,6IAAA,CAAA,OAAI;;;;;kCACL,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,gJAAA,CAAA,UAAO;gCAAC,OAAM;0CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,OAAO;8CACzC,cAAA,sSAAC,wQAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;kDAEP,cAAA,sSAAC,mUAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;0CAIrB,sSAAC,wJAAA,CAAA,cAAW;;;;;0CACZ,sSAAC,sQAAA,CAAA,WAAQ;0CACP,cAAA,sSAAC,2JAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;0BAIrB,sSAAC;;;;;;;;;;;AAGP;MAzBwB", "debugId": null}}]}